"use client";
import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { CalendarIcon, Star, DollarSign, User, Timer } from "lucide-react";
import { format, addDays, set } from "date-fns";
import { vi } from "date-fns/locale";
import { Label } from "@/components/ui/label";
import { DEFAULT_POST_PRICE, MemberRank, PropertyStatus } from "@/lib/enum";
import BadgeStatus from "../layout/BadgeStatus";
import BadgeUserRank from "../layout/BadgeUserRank";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { getHighlightPriceNumber } from "@/lib/memberRankUtils";
import { FormControl, FormDescription, FormField, FormItem, FormLabel } from "../ui/form";
import { Separator } from "@/components/ui/separator";

export default function CreatePropertyDetailInformation({
  form,
  property,
  isFormDisabled,
  basePostPrice = DEFAULT_POST_PRICE,
  onRankChange = () => {},
  onRefreshRef = null,
}) {
  const t = useTranslations("CreatePropertyDetailInformation");
  const tCommon = useTranslations("Common");
  const { profile } = useAuth();
  const displayDuration = 10; // days

  // Get the user's rank from profile context
  const userRankFromProfile = profile?.user?.memberRank || MemberRank.DEFAULT;

  // State to track the current rank (can be updated by BadgeUserRank component)
  const [currentRank, setCurrentRank] = useState(userRankFromProfile);

  const status = property?.status || PropertyStatus.DRAFT;
  const statusText = property?.status ? tCommon(`propertyStatus_${property?.status}`) : tCommon(`propertyStatus_${PropertyStatus.DRAFT}`);
  const createdAt = property?.createdAt || new Date();
  const expiresAt = property?.expiresAt;
  const highlight = form.watch("isHighlighted") || false;

  // Update currentRank when profile changes
  useEffect(() => {
    if (profile?.user?.memberRank) {
      setCurrentRank(profile.user.memberRank);
    }
  }, [profile]);

  // Calculate highlight price using the utility function
  const highlightPrice = getHighlightPriceNumber(currentRank);
  const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);

  const expirationDate = expiresAt || addDays(createdAt, displayDuration);

  return (
    <div className="space-y-6">
      {/* Customer Information Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-600" />
          <h3 className="font-medium text-sm text-gray-900">{t("individualCustomer")}</h3>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 space-y-2">
          <Label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t("memberRank")}</Label>
          <BadgeUserRank
            showRefreshButton={true}
            onRankChange={(rankData) => {
              // Only update if the rank has actually changed
              if (rankData.currentRank !== currentRank) {
                setCurrentRank(rankData.currentRank);
                onRankChange(rankData);
              }
            }}
            onRefreshRef={onRefreshRef}
          />
        </div>
      </div>

      {/* Post Options Section */}
      {!isFormDisabled && (
        <div className="space-y-4">
          <Separator />
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="isHighlighted"
              render={({ field }) => (
                <FormItem className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-600" />
                        <FormLabel className="font-medium text-gray-900">{t("highlightPost")}</FormLabel>
                      </div>
                      <FormDescription className="text-sm text-gray-600">
                        {t("highlightPostDescription")}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-yellow-600"
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isAutoRenew"
              render={({ field }) => (
                <FormItem className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Timer className="h-4 w-4 text-green-600" />
                        <FormLabel className="font-medium text-gray-900">{t("autoRenew")}</FormLabel>
                      </div>
                      <FormDescription className="text-sm text-gray-600">
                        {t("autoRenewDescription")}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-green-600"
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />
          </div>
        </div>
      )}

      {/* Post Information Section */}
      <div className="space-y-4">
        <Separator />
        <div className="bg-blue-50 rounded-lg p-4 space-y-4">
          <h4 className="font-medium text-gray-900 text-sm">{t("postStatus")}</h4>
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t("postStatus")}</Label>
              <BadgeStatus status={status} statusText={statusText} />
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t("expirationTime")}</Label>
              <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
                <Timer className="h-3 w-3 text-blue-600" />
                {displayDuration} {t("days")}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t("postDate")}</Label>
              <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
                <CalendarIcon className="h-3 w-3 text-blue-600" />
                {format(createdAt, "dd/MM/yyyy", { locale: vi })}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t("expirationDate")}</Label>
              <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
                <CalendarIcon className="h-3 w-3 text-blue-600" />
                {format(expirationDate, "dd/MM/yyyy", { locale: vi })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="space-y-4">
        <Separator />
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-2 mb-3">
            <DollarSign className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-gray-900 text-sm">{t("postFee")}</h4>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center py-2">
              <span className="text-sm text-gray-600">{t("postFee")}</span>
              <span className="font-medium text-gray-900">{basePostPrice.toLocaleString("vi-VN")} đ</span>
            </div>

            {highlight && (
              <div className="flex justify-between items-center py-2 bg-yellow-50 rounded-md px-3 border border-yellow-200">
                <div className="flex items-center gap-2">
                  <Star className="h-3 w-3 text-yellow-600" />
                  <span className="text-sm text-gray-600">
                    {t("highlightFee")}
                  </span>
                </div>
                <span className="font-medium text-yellow-700">+{highlightPrice.toLocaleString("vi-VN")} đ</span>
              </div>
            )}

            <Separator />

            <div className="flex justify-between items-center py-2 bg-green-50 rounded-md px-3 border border-green-200">
              <span className="font-semibold text-gray-900">{t("total")}</span>
              <span className="font-bold text-lg text-green-700">{totalPrice.toLocaleString("vi-VN")} đ</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

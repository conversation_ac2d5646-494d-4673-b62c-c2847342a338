"use client";
import { useState, useEffect, useCallback } from "react";
import { MemberRank } from "@/lib/enum";
import { Badge } from "../ui/badge";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "../ui/button";
import { RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";
import { getMemberRankTranslationKey } from "@/lib/memberRankUtils";
import { useToast } from "@/hooks/use-toast";

// Use translation keys for rank names from Common namespace
const getRankDetails = (tCommon) => ({
  [MemberRank.DIAMOND]: { text: tCommon(getMemberRankTranslationKey(MemberRank.DIAMOND)), color: "bg-blue-500 text-white hover:bg-blue-500" },
  [MemberRank.PLATINUM]: { text: tCommon(getMemberRankTranslationKey(MemberRank.PLATINUM)), color: "bg-slate-300 text-black hover:bg-slate-300" },
  [MemberRank.GOLD]: { text: tCommon(getMemberRankTranslationKey(MemberRank.GOLD)), color: "bg-yellow-500 text-black hover:bg-yellow-500" },
  [MemberRank.SILVER]: { text: tCommon(getMemberRankTranslationKey(MemberRank.SILVER)), color: "bg-gray-400 text-black hover:bg-gray-400" },
  [MemberRank.BRONZE]: { text: tCommon(getMemberRankTranslationKey(MemberRank.BRONZE)), color: "bg-amber-700 text-white hover:bg-amber-700" },
  [MemberRank.DEFAULT]: { text: tCommon(getMemberRankTranslationKey(MemberRank.DEFAULT)), color: "bg-gray-600 text-white hover:bg-gray-600" },
});

export default function BadgeUserRank({
  className,
  showRefreshButton = false,
  onRankChange = null,
  onRefreshRef = null,
  externalRank = null, // Optional prop to override the rank from context
}) {
  const tCommon = useTranslations("Common");
  const t = useTranslations("PropertyForm");
  const { toast } = useToast();
  const { profile, refreshProfile } = useAuth();
  const [initialRank, setInitialRank] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use external rank if provided, otherwise use memberRank from profile
  const userRank = externalRank !== null ? externalRank : profile?.user?.memberRank;

  // Function to manually refresh user rank data
  const refreshRankData = useCallback(async () => {
    if (isRefreshing) return; // Prevent multiple simultaneous refreshes

    setIsRefreshing(true);
    try {
      await refreshProfile();
      toast({
        title: t("rankUpdated"),
        description: t("rankRefreshed"),
        className: "bg-teal-600 text-white",
      });
    } catch (error) {
      console.error("Error refreshing rank data:", error);
      toast({
        variant: "destructive",
        title: t("error") || "Error",
        description: t("errorRefreshingRank") || "Error refreshing rank data",
      });
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshProfile, toast, t, isRefreshing]);

  // Expose the refresh function via ref if provided
  useEffect(() => {
    if (onRefreshRef) {
      onRefreshRef.current = refreshRankData;
    }
  }, [onRefreshRef, refreshRankData]);

  // Handle rank changes
  useEffect(() => {
    if (userRank && onRankChange) {
      // Set initial rank on first load
      if (initialRank === null) {
        setInitialRank(userRank);
      }

      // Check if rank has changed since component mounted
      if (initialRank !== null && initialRank !== userRank) {
        onRankChange({
          previousRank: initialRank,
          currentRank: userRank,
        });
      }
    }
  }, [userRank, initialRank, onRankChange]);

  // Get badge details based on rank with translations
  const rankDetails = getRankDetails(tCommon);
  const { text, color } = rankDetails[userRank] || rankDetails[MemberRank.DEFAULT];

  return (
    <div className="flex items-center gap-2">
      {userRank && (
        <Badge className={cn(color, "font-medium rounded-md px-2 py-1 text-sm", className)}>
          {text}
        </Badge>
      )}

      {!userRank && (
        <Badge className="font-xs rounded-md px-2 py-1 text-sm" variant="outline">
          ...loading
        </Badge>
      )}

      {showRefreshButton && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            refreshRankData();
          }}
          disabled={isRefreshing}
          className="flex items-center gap-1 text-xs h-6 px-2"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="h-3 w-3 animate-spin" />
              {t("refreshing")}
            </>
          ) : (
            <>
              <RefreshCw className="h-3 w-3" />
              {t("refreshRank")}
            </>
          )}
        </Button>
      )}
    </div>
  );
}

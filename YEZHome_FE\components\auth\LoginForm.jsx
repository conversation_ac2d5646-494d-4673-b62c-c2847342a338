"use client";

import { useState, useEffect } from "react";
import { Eye, EyeOff, CircleAlert } from "lucide-react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { useActionState } from "react";
import { loginUser } from "@/app/actions/server/authenticate";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useRouter } from "next/navigation";

const initialState = {
  errors: {},
  message: null,
  fieldValues: {
    email: "",
    password: "",
  },
};

export default function LoginForm() {
  const t = useTranslations('LoginPage');
  const [showPassword, setShowPassword] = useState(false);
  const [state, formAction, isPending] = useActionState(loginUser, initialState);
  const router = useRouter();

  // Handle successful login
  useEffect(() => {
    if (state?.success) {
      // Dispatch event to update AuthContext
      window.dispatchEvent(new CustomEvent('user-logged-in'));

      // Redirect after a short delay
      setTimeout(() => {
        router.push(state.redirectUrl || "/user/profile");
      }, 100);
    }
  }, [state, router]);

  return (
    <form className="mt-8 space-y-6" action={formAction}>
      {state?.message && (
        <Alert variant="destructive">
          <CircleAlert className="h-4 w-4" />
          <AlertTitle>{t('loginErrorTitle')}</AlertTitle>
          <AlertDescription>{state?.message}</AlertDescription>
        </Alert>
      )}
      <div className="rounded-md shadow-sm -space-y-px">
        <div>
          <label htmlFor="email" className="sr-only">
            {t('emailLabel')}
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            defaultValue={state.fieldValues?.email}
            className="appearance-none rounded-t-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-none focus:ring-coral focus:border-teal-700 focus:z-10 sm:text-sm"
            placeholder={t('emailPlaceholder')}
          />
          {state.errors?.email && (
            <p className="mt-1 text-xs text-red-500">{state.errors.email[0]}</p>
          )}
        </div>
        <div className="relative">
          <label htmlFor="password" className="sr-only">
            {t('passwordLabel')}
          </label>
          <input
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete="current-password"
            required
            defaultValue={state.fieldValues?.password}
            className="appearance-none rounded-b-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-none focus:ring-coral focus:border-teal-700 focus:z-10 sm:text-sm"
            placeholder={t('passwordPlaceholder')}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5 text-gray-400" />
            ) : (
              <Eye className="h-5 w-5 text-gray-400" />
            )}
          </button>
          {state.errors?.password && (
            <p className="mt-1 text-xs text-red-500">{state.errors.password[0]}</p>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 text-coral-500 focus:ring-coral border-gray-300 rounded"
          />
          <label htmlFor="remember-me" className="ml-2 block text-sm text-charcoal">
            {t('rememberMeLabel')}
          </label>
        </div>

        <div className="text-sm">
          <Link href="/quen-mat-khau" className="font-medium text-coral-500 hover:text-coral-600">
            {t('forgotPasswordLink')}
          </Link>
        </div>
      </div>

      <div>
        <ButtonLoading type="submit" showLoading={isPending} title={t('loginButton')} />
      </div>
    </form>
  );
}
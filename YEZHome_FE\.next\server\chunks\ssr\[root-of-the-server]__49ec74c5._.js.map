{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_a5770df4.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_a5770df4-module__U1nkFW__className\",\n  \"variable\": \"inter_a5770df4-module__U1nkFW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_a5770df4.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.jsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22weight%22:[%22400%22,%22700%22],%22subsets%22:[%22vietnamese%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/i18n/routing.ts"], "sourcesContent": ["import {defineRouting} from 'next-intl/routing';\r\n \r\nexport const routing = defineRouting({\r\n  // A list of all locales that are supported\r\n  locales: ['en', 'vi'],\r\n \r\n  // Used when no locale matches\r\n  defaultLocale: 'vi'\r\n});"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/i18n/request.ts"], "sourcesContent": ["import {getRequestConfig} from 'next-intl/server';\r\nimport {hasLocale} from 'next-intl';\r\nimport {routing} from './routing';\r\n \r\nexport default getRequestConfig(async ({requestLocale}) => {\r\n  // Typically corresponds to the `[locale]` segment\r\n  const requested = await requestLocale;\r\n  const locale = hasLocale(routing.locales, requested)\r\n    ? requested\r\n    : routing.defaultLocale;\r\n \r\n  return {\r\n    locale,\r\n    messages: (await import(`../messages/${locale}.json`)).default\r\n  };\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAC,aAAa,EAAC;IACpD,kDAAkD;IAClD,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,+GAAA,CAAA,UAAO,CAAC,OAAO,EAAE,aACtC,YACA,+GAAA,CAAA,UAAO,CAAC,aAAa;IAEzB,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/i18n/navigation.ts"], "sourcesContent": ["import {createNavigation} from 'next-intl/navigation';\r\nimport {routing} from './routing';\r\n \r\n// Lightweight wrappers around Next.js' navigation\r\n// APIs that consider the routing configuration\r\nexport const {Link, redirect, usePathname, useRouter, getPathname} =\r\n  createNavigation(routing);"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAIO,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAC,GAChE,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE,+GAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/Footer.jsx"], "sourcesContent": ["import { Mail, Phone } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport {Link} from '@/i18n/navigation';;\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <footer className=\"bg-navy-blue p-8\">\r\n      <div className=\"max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n        {/* Left Section - Company Info */}\r\n        <div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Image src=\"/yezhome_logo.png\" alt=\"Logo YezHome\" width={150} height={150} className=\"h-15 p-1\" />\r\n          </div>\r\n          <p className=\"text-coral-500 mt-2\">\r\n            <strong>YEZHOME VIỆT NAM</strong>\r\n          </p>\r\n          <p className=\"text-sm text-white\">Tầng 100, Landmark 100, Hồ Chí Minh city</p>\r\n          <p className=\"text-sm text-white\">(084) 1234 1234 - (084) 2345 2345</p>\r\n          {/* QR Code & App Links */}\r\n          <div className=\"flex items-center space-x-4 mt-4\">\r\n            <img src=\"/qrcode.png\" alt=\"QR Code\" className=\"h-16 bg-white rounded-sm\" />\r\n            <div className=\"flex flex-col space-y-2\">\r\n              <img src=\"/google-play.png\" alt=\"Google Play\" className=\"h-8\" />\r\n              <img src=\"/app-store.png\" alt=\"App Store\" className=\"h-8\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Middle Section - Support & Policies */}\r\n        <div className=\"grid grid-cols-2 gap-6\">\r\n          <div>\r\n            <h3 className=\"text-coral-500 font-semibold\">HƯỚNG DẪN</h3>\r\n            <ul className=\"text-sm text-white space-y-1 mt-2\">\r\n              <li>\r\n                <Link href=\"/about\" className=\"hover:text-teal-600\">\r\n                  Về chúng tôi\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/pricing\" className=\"hover:text-teal-600\">\r\n                  Báo giá và hỗ trợ\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/faq\" className=\"hover:text-teal-600\">\r\n                  Câu hỏi thường gặp\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/feedback\" className=\"hover:text-teal-600\">\r\n                  Góp ý báo lỗi\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/sitemap\" className=\"hover:text-teal-600\">\r\n                  Sitemap\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"text-coral-500 font-semibold\">QUY ĐỊNH</h3>\r\n            <ul className=\"text-sm text-white space-y-1 mt-2\">\r\n              <li>\r\n                <Link href=\"/rules/posting\" className=\"hover:text-teal-600\">\r\n                  Quy định đăng tin\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/rules/operation\" className=\"hover:text-teal-600\">\r\n                  Quy chế hoạt động\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/terms\" className=\"hover:text-teal-600\">\r\n                  Điều khoản thỏa thuận\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/privacy\" className=\"hover:text-teal-600\">\r\n                  Chính sách bảo mật\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/complaints\" className=\"hover:text-teal-600\">\r\n                  Giải quyết khiếu nại\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Section - Contact & Subscription */}\r\n        <div>\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center space-x-2 text-white\">\r\n              <Phone />\r\n              <span className=\"font-semibold\">Hotline: 1900 1234</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-white\">\r\n              <Mail />\r\n              <a href=\"mailto:trogiup.batdongsan.com.vn\" className=\"text-sm\">\r\n                trogiup.yezhome.com.vn\r\n              </a>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-white\">\r\n              <Mail />\r\n              <a href=\"mailto:<EMAIL>\" className=\"text-sm\">\r\n                <EMAIL>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Email Subscription */}\r\n          <div className=\"mt-4\">\r\n            <h3 className=\"text-coral-500 font-semibold\">ĐĂNG KÝ NHẬN TIN</h3>\r\n            <div className=\"flex mt-2\">\r\n              <input\r\n                type=\"email\"\r\n                placeholder=\"Nhập email của bạn\"\r\n                className=\"p-2 border rounded-l w-full\"\r\n              />\r\n              <button className=\"bg-red-500 text-white px-4 rounded-r\">➤</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"text-center text-sm text-white mt-8\">\r\n        © 2025 YEZHOME. Tất cả các quyền được bảo lưu.\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAI;oCAAoB,KAAI;oCAAe,OAAO;oCAAK,QAAQ;oCAAK,WAAU;;;;;;;;;;;0CAEvF,8OAAC;gCAAE,WAAU;0CACX,cAAA,8OAAC;8CAAO;;;;;;;;;;;0CAEV,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,KAAI;wCAAc,KAAI;wCAAU,WAAU;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,KAAI;gDAAmB,KAAI;gDAAc,WAAU;;;;;;0DACxD,8OAAC;gDAAI,KAAI;gDAAiB,KAAI;gDAAY,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAsB;;;;;;;;;;;0DAItD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAsB;;;;;;;;;;;0DAIxD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAO,WAAU;8DAAsB;;;;;;;;;;;0DAIpD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAsB;;;;;;;;;;;0DAIzD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAiB,WAAU;8DAAsB;;;;;;;;;;;0DAI9D,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAmB,WAAU;8DAAsB;;;;;;;;;;;0DAIhE,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAsB;;;;;;;;;;;0DAItD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAsB;;;;;;;;;;;0DAIxD,8OAAC;0DACC,cAAA,8OAAC,kHAAA,CAAA,OAAI;oDAAC,MAAK;oDAAc,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjE,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;;;;;0DACN,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;;;;;0DACL,8OAAC;gDAAE,MAAK;gDAAmC,WAAU;0DAAU;;;;;;;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;;;;;0DACL,8OAAC;gDAAE,MAAK;gDAAiC,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAOjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAO,WAAU;0DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKjE,8OAAC;gBAAI,WAAU;0BAAsC;;;;;;;;;;;;AAK3D", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/ClientNavbar.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientNavbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientNavbar() from the server but ClientNavbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/ClientNavbar.jsx <module evaluation>\",\n    \"ClientNavbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oEACA", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/ClientNavbar.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientNavbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientNavbar() from the server but ClientNavbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/ClientNavbar.jsx\",\n    \"ClientNavbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gDACA", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/Navbar.jsx"], "sourcesContent": ["import { Link } from \"@/i18n/navigation\";\r\nimport Image from \"next/image\";\r\nimport { getTranslations } from \"next-intl/server\";\r\nimport { ClientNavbar } from \"./ClientNavbar\";\r\n\r\n// Modify NavBarData to use translation keys\r\nconst NavBarData = [\r\n  { id: 1, url: \"/\", nameKey: \"Navbar.linkHome\" }, // Assuming root is home\r\n  { id: 2, url: \"/?postType=sell\", nameKey: \"Navbar.linkBuy\" }, // Updated URL for Buy\r\n  { id: 3, url: \"/?postType=rent\", nameKey: \"Navbar.linkRent\" }, // Updated URL for Rent\r\n  { id: 4, url: \"/tin-tuc\", nameKey: \"Navbar.linkNews\" },\r\n  { id: 5, url: \"/bieu-phi\", nameKey: \"Navbar.linkPricing\" },\r\n  { id: 6, url: \"/gioi-thieu\", nameKey: \"Navbar.linkAbout\" },\r\n  { id: 7, url: \"/lien-he\", nameKey: \"Navbar.linkContact\" },\r\n];\r\n\r\n// NavLinks now accepts and uses the translation function\r\nconst NavLinks = ({ t }) => (\r\n  <>\r\n    {NavBarData.map((item) => (\r\n      <Link key={item.id} href={item.url} className=\"text-navy-blue hover:text-coral-600 font-semibold\">\r\n        {t(item.nameKey)} {/* Use translation key */}\r\n      </Link>\r\n    ))}\r\n  </>\r\n);\r\n\r\nexport default async function Navbar({ isLoggedIn = false }) {\r\n  const t = await getTranslations(); // Get translation function\r\n  \r\n  return (\r\n    <nav className=\"flex sticky top-0 z-50 w-full items-center border-b bg-background\">\r\n      <div className=\"mx-auto flex justify-between items-center w-full\">\r\n        <div className=\"flex items-center space-x-12\">\r\n          <Link href=\"/\" className=\"flex items-center space-x-3 pr-6\">\r\n            <Image\r\n              src=\"/yezhome_logo.png\"\r\n              alt={t(\"Navbar.logoAlt\")} // Use translated alt text\r\n              width={150}\r\n              height={150}\r\n              className=\"p-3\"\r\n              priority={true}\r\n              quality={90}\r\n            />\r\n          </Link>\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex space-x-9 items-center pt-2\">\r\n            <NavLinks t={t} /> {/* Pass translation function */}\r\n          </div>\r\n        </div>\r\n        <ClientNavbar isLoggedIn={isLoggedIn} />\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,4CAA4C;AAC5C,MAAM,aAAa;IACjB;QAAE,IAAI;QAAG,KAAK;QAAK,SAAS;IAAkB;IAC9C;QAAE,IAAI;QAAG,KAAK;QAAmB,SAAS;IAAiB;IAC3D;QAAE,IAAI;QAAG,KAAK;QAAmB,SAAS;IAAkB;IAC5D;QAAE,IAAI;QAAG,KAAK;QAAY,SAAS;IAAkB;IACrD;QAAE,IAAI;QAAG,KAAK;QAAa,SAAS;IAAqB;IACzD;QAAE,IAAI;QAAG,KAAK;QAAe,SAAS;IAAmB;IACzD;QAAE,IAAI;QAAG,KAAK;QAAY,SAAS;IAAqB;CACzD;AAED,yDAAyD;AACzD,MAAM,WAAW,CAAC,EAAE,CAAC,EAAE,iBACrB;kBACG,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,kHAAA,CAAA,OAAI;gBAAe,MAAM,KAAK,GAAG;gBAAE,WAAU;;oBAC3C,EAAE,KAAK,OAAO;oBAAE;;eADR,KAAK,EAAE;;;;;;AAOT,eAAe,OAAO,EAAE,aAAa,KAAK,EAAE;IACzD,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,KAAK,2BAA2B;IAE9D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kHAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAK,EAAE;gCACP,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,UAAU;gCACV,SAAS;;;;;;;;;;;sCAIb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAS,GAAG;;;;;;gCAAK;;;;;;;;;;;;;8BAGtB,8OAAC,qIAAA,CAAA,eAAY;oBAAC,YAAY;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/AlertPopup.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/AlertPopup.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/AlertPopup.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/AlertPopup.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/AlertPopup.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/AlertPopup.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/ProfileContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProfileContextProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProfileContextProvider() from the server but ProfileContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/ProfileContext.jsx <module evaluation>\",\n    \"ProfileContextProvider\",\n);\nexport const useProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProfile() from the server but useProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/ProfileContext.jsx <module evaluation>\",\n    \"useProfile\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,6DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6DACA", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/ProfileContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProfileContextProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProfileContextProvider() from the server but ProfileContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/ProfileContext.jsx\",\n    \"ProfileContextProvider\",\n);\nexport const useProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProfile() from the server but useProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/ProfileContext.jsx\",\n    \"useProfile\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,yCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yCACA", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/ProfileProvider.jsx"], "sourcesContent": ["import { ProfileContextProvider } from \"@/contexts/ProfileContext\";\r\n\r\nexport default async function ProfileProvider({ children }) {\r\n    return <ProfileContextProvider>{children}</ProfileContextProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,eAAe,gBAAgB,EAAE,QAAQ,EAAE;IACtD,qBAAO,8OAAC,2HAAA,CAAA,yBAAsB;kBAAE;;;;;;AACpC", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AlertContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AlertProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AlertProvider() from the server but AlertProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AlertContext.jsx <module evaluation>\",\n    \"AlertProvider\",\n);\nexport const useAlert = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAlert() from the server but useAlert is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AlertContext.jsx <module evaluation>\",\n    \"useAlert\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2DACA", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AlertContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AlertProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AlertProvider() from the server but AlertProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AlertContext.jsx\",\n    \"AlertProvider\",\n);\nexport const useAlert = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAlert() from the server but useAlert is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AlertContext.jsx\",\n    \"useAlert\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,uCACA", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/toaster.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toaster.jsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2DACA", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/toaster.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toaster.jsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uCACA", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AuthContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.jsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.jsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AuthContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.jsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.jsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/apiUtils.js"], "sourcesContent": ["export const handleErrorResponse = (success, fieldValues = null, message = null) => ({\r\n  success,\r\n  message,\r\n  fieldValues,  \r\n  timestamp: new Date().toISOString()\r\n});\r\n\r\nexport const logError = (serviceName, error, additionalContext = {}) => {\r\n  console.error(`[${serviceName}] Error:`, {\r\n    message: error.message,\r\n    stack: error.stack,\r\n    ...additionalContext,\r\n    timestamp: new Date().toISOString()\r\n  });\r\n};"], "names": [], "mappings": ";;;;AAAO,MAAM,sBAAsB,CAAC,SAAS,cAAc,IAAI,EAAE,UAAU,IAAI,GAAK,CAAC;QACnF;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC,CAAC;AAEM,MAAM,WAAW,CAAC,aAAa,OAAO,oBAAoB,CAAC,CAAC;IACjE,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,EAAE;QACvC,SAAS,MAAM,OAAO;QACtB,OAAO,MAAM,KAAK;QAClB,GAAG,iBAAiB;QACpB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/schemas/authSchema.jsx"], "sourcesContent": ["import { z } from \"zod\"\r\n\r\nexport const registerSchema = z.object({\r\n  fullname: z.string().min(3, \"<PERSON><PERSON> tên phải có ít nhất 3 ký tự\"),\r\n  email: z.string().email(\"Định dạng email không đúng\"),\r\n  phone: z.string().min(10, \"Số điện thoại không hợp lệ\"),\r\n  userType: z.enum([\"Buyer\", \"Seller\"], { required_error: \"Chọn 1 trong 2\" }),\r\n  password: z.string().min(6, \"Mật khẩu phải có ít nhất 6 ký tự\"),\r\n  confirmPassword: z.string().min(6, \"<PERSON>ui lòng xác nhận mật khẩu\"),\r\n}).refine((data) => data.password === data.confirmPassword, {\r\n  message: \"Passwords do not match\",\r\n  path: [\"confirmPassword\"], // This tells <PERSON><PERSON> to add the error to the confirmPassword field\r\n})\r\n\r\nexport const loginSchema = z.object({\r\n  email: z.string({ required_error: \"Bắt buộc nhập.\" }).email(\"Định dạng email không đúng\"),\r\n  password: z.string({ required_error: \"Bắt buộc nhập.\" }).min(6, \"Mật khẩu phải có ít nhất 6 ký tự\"),\r\n})\r\n\r\nexport const changePasswordSchema = z.object({\r\n  oldPassword: z.string().min(1, \"Vui lòng nhập mật khẩu cũ\"),\r\n  newPassword: z.string().min(6, \"Mật khẩu mới phải có ít nhất 6 ký tự\"),\r\n  confirmPassword: z.string().min(6, \"Vui lòng xác nhận mật khẩu mới\"),\r\n}).refine((data) => data.newPassword === data.confirmPassword, {\r\n  message: \"Mật khẩu mới không khớp\",\r\n  path: [\"confirmPassword\"],\r\n});\r\n\r\nexport const forgetPasswordSchema = z.object({\r\n  email: z.string({required_error: \"Vui lòng nhập địa chỉ mail\"}).email(\"Định dạng email không đúng\"),\r\n});"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAS,EAAE;QAAE,gBAAgB;IAAiB;IACzE,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEO,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,gBAAgB;IAAiB,GAAG,KAAK,CAAC;IAC5D,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,gBAAgB;IAAiB,GAAG,GAAG,CAAC,GAAG;AAClE;AAEO,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC7D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEO,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAC,gBAAgB;IAA4B,GAAG,KAAK,CAAC;AACxE", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/sessionUtils.js"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { jwtDecode } from \"jwt-decode\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { jwtVerify } from \"jose\";\r\n\r\nconst SECRET_KEY_STRING = process.env.JWT_SECRET;\r\n\r\nconst SECRET_KEY = new TextEncoder().encode(SECRET_KEY_STRING);\r\n\r\nexport async function createSession(name, value, options) {\r\n  const cookieStore = await cookies();\r\n\r\n  const defaultOptions = {\r\n    secure: true,\r\n    httpOnly: true,\r\n    expires: Date.now() + 24 * 60 * 60 * 1000, // 1 days\r\n    path: \"/\",\r\n    sameSite: \"strict\",\r\n  };\r\n\r\n  cookieStore.set(name, value, {\r\n    ...defaultOptions,\r\n    ...options,\r\n  });\r\n}\r\n\r\nexport async function getSession(name) {\r\n  const cookieStore = await cookies();\r\n\r\n  return cookieStore.get(name)?.value;\r\n}\r\n\r\nexport async function deleteSession(name) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(name);\r\n}\r\n\r\nexport async function fetchWithAuth(url, options = {}) {\r\n  try {\r\n    const token = await getSession(\"Authorization\"); // Lấy token từ session\r\n    if (!token) {\r\n      console.log(\"No token found\");\r\n      return {\r\n        success: false,\r\n        errorType: \"no_token\",\r\n        message: \"User is not logged in.\",\r\n      };\r\n    }\r\n\r\n    console.log(`[${options.method}]: ${url} - Fetching with auth token: ${url} `);\r\n    console.log(`BODY: ${options?.body}`);\r\n\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n      credentials: \"include\",\r\n    });\r\n\r\n    // Xử lý lỗi 401 (Unauthorized)\r\n    if (response.status === 401) {\r\n      try {\r\n        console.log(response);\r\n        // Xử lý lỗi token hết hạn nếu có 401 nhưng token vẫn có trong cooike thì xóa token => vì token đã hết hạn\r\n        if (token) {\r\n          console.warn(`Token expired. Clearing session...${url}`);\r\n          return {\r\n            success: false,\r\n            errorType: \"token_expired\",\r\n            message: \"Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại.\",\r\n          };\r\n        }\r\n      } catch (error) {\r\n        console.warn(\"Lỗi khi xử lý phản hồi 401:\", error);\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        errorType: \"unauthorized\",\r\n        message: \"401. Vui lòng đăng nhập.\",\r\n      };\r\n    }\r\n\r\n    // Xử lý lỗi chung\r\n    if (!response.ok) {\r\n      console.error(`API request failed: ${response.status} ${response.statusText}`);\r\n\r\n      const errorData = await response.json();\r\n      console.error(\"Error data:\", errorData);\r\n\r\n      let message = `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`;\r\n      if (errorData?.detail || errorData?.message) {\r\n        message = errorData?.detail || errorData?.message;\r\n      } else if (typeof errorData === \"object\") {\r\n        for (const key in errorData) {\r\n          if (errorData.hasOwnProperty(key)) {\r\n            message = errorData[key];\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        errorType: (errorData && errorData?.errorType) || \"api_error\",\r\n        message: message,\r\n      };\r\n    }\r\n\r\n    // Nếu gọi API thành công, trả về No Content 204 hoặc 201\r\n    if (response.status === 204 || response.status === 201) {\r\n      return {\r\n        success: true,\r\n        data: null,\r\n      };\r\n    } else {\r\n      // Nếu gọi API thành công, trả về dữ liệu chuẩn\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Fetch error:\", error);\r\n    return {\r\n      success: false,\r\n      errorType: \"network_error\",\r\n      message:\r\n        errorData &&\r\n        (errorData?.detail ||\r\n          errorData?.message ||\r\n          `Code: ${response?.status} - ${response?.statusText}: Failed to connect to the server. Please try again later.`),\r\n    };\r\n  }\r\n}\r\n\r\nexport async function fetchWithoutAuth(url, options = {}) {\r\n  try {\r\n    console.log(`[${options.method || \"GET\"}]: ${url} - Fetching without auth`);\r\n    if (options?.body) {\r\n      console.log(`BODY: ${options.body}`);\r\n    }\r\n\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        ...(options.headers || {}),\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(`Public API request failed: ${response.status} ${response.statusText}`);\r\n      let errorData;\r\n      try {\r\n        errorData = await response.json();\r\n      } catch (e) {\r\n        errorData = { message: response.statusText };\r\n      }\r\n      return {\r\n        success: false,\r\n        errorType: (errorData && errorData?.errorType) || \"api_error\",\r\n        message: errorData?.detail || errorData?.message || `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`,\r\n      };\r\n    }\r\n\r\n    if (response.status === 204 || response.status === 201) {\r\n      return {\r\n        success: true,\r\n        data: null,\r\n      };\r\n    } else {\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Fetch error (without auth):\", error);\r\n    return {\r\n      success: false,\r\n      errorType: \"network_error\",\r\n      message: `Failed to connect to the server. Please try again later. (${error.message || \"Unknown network error\"})`,\r\n    };\r\n  }\r\n}\r\n\r\nexport async function clearSessionAndBackToLogin() {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(\"Authorization\"); // Remove auth token\r\n  redirect(\"/dang-nhap\"); // Redirect to login page\r\n}\r\n\r\nexport async function getJwtInfo() {\r\n  const cookieStore = await cookies();\r\n  const decodedToken = jwtDecode(cookieStore.get(\"Authorization\")?.value);\r\n  return decodedToken;\r\n}\r\n\r\nexport async function verifyJwtToken(token) {\r\n  try {\r\n    const { payload } = await jwtVerify(token, SECRET_KEY);\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    // Xử lý các lỗi cụ thể do 'jose' ném ra (TokenExpiredError, JWSInvalid...)\r\n    // Các lỗi này là runtime error do bản thân token, không phải lỗi cấu hình\r\n    // Log các lỗi này ở mức độ thấp hơn (warn, error)\r\n    if (error.name === 'JOSEError' && error.message === 'signature verification failed') {\r\n         console.warn('JWT verification failed: Invalid signature.');\r\n    } else if (error.name === 'JWTExpired') {\r\n         console.warn('JWT verification failed: Token has expired.');\r\n         // Bạn có thể return một giá trị đặc biệt hoặc ném lỗi khác\r\n         // nếu middleware cần phân biệt hết hạn và invalid signature\r\n    } else {\r\n      console.error('Unexpected JWT verification error:', error);\r\n    }\r\n\r\n    return null; // Trả về null nếu token không hợp lệ hoặc hết hạn\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AACA;;;;;;;;AAEA,MAAM,oBAAoB,QAAQ,GAAG,CAAC,UAAU;AAEhD,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;AAErC,eAAe,cAAc,IAAI,EAAE,KAAK,EAAE,OAAO;IACtD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB,QAAQ;QACR,UAAU;QACV,SAAS,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;QACrC,MAAM;QACN,UAAU;IACZ;IAEA,YAAY,GAAG,CAAC,MAAM,OAAO;QAC3B,GAAG,cAAc;QACjB,GAAG,OAAO;IACZ;AACF;AAEO,eAAe,WAAW,IAAI;IACnC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAA<PERSON>,CAAA,UAAO,AAAD;IAEhC,OAAO,YAAY,GAAG,CAAC,OAAO;AAChC;AAEO,eAAe,cAAc,IAAI;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAEO,eAAe,cAAc,GAAG,EAAE,UAAU,CAAC,CAAC;IACnD,IAAI;QACF,MAAM,QAAQ,MAAM,WAAW,kBAAkB,uBAAuB;QACxE,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,GAAG,EAAE,IAAI,6BAA6B,EAAE,IAAI,CAAC,CAAC;QAC7E,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,MAAM;QAEpC,MAAM,YAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACpB;YACA,aAAa;QACf;QAEA,+BAA+B;QAC/B,IAAI,UAAS,MAAM,KAAK,KAAK;YAC3B,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,0GAA0G;gBAC1G,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,CAAC,kCAAkC,EAAE,KAAK;oBACvD,OAAO;wBACL,SAAS;wBACT,WAAW;wBACX,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,+BAA+B;YAC9C;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;YACX;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,UAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,UAAS,MAAM,CAAC,CAAC,EAAE,UAAS,UAAU,EAAE;YAE7E,MAAM,aAAY,MAAM,UAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,eAAe;YAE7B,IAAI,UAAU,CAAC,MAAM,EAAE,UAAS,MAAM,CAAC,GAAG,EAAE,UAAS,UAAU,CAAC,kCAAkC,CAAC;YACnG,IAAI,YAAW,UAAU,YAAW,SAAS;gBAC3C,UAAU,YAAW,UAAU,YAAW;YAC5C,OAAO,IAAI,OAAO,eAAc,UAAU;gBACxC,IAAK,MAAM,OAAO,WAAW;oBAC3B,IAAI,WAAU,cAAc,CAAC,MAAM;wBACjC,UAAU,UAAS,CAAC,IAAI;wBACxB;oBACF;gBACF;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW,AAAC,cAAa,YAAW,aAAc;gBAClD,SAAS;YACX;QACF;QAEA,yDAAyD;QACzD,IAAI,UAAS,MAAM,KAAK,OAAO,UAAS,MAAM,KAAK,KAAK;YACtD,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,+CAA+C;YAC/C,MAAM,OAAO,MAAM,UAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,WAAW;YACX,SACE,aACA,CAAC,WAAW,UACV,WAAW,WACX,CAAC,MAAM,EAAE,UAAU,OAAO,GAAG,EAAE,UAAU,WAAW,0DAA0D,CAAC;QACrH;IACF;AACF;AAEO,eAAe,iBAAiB,GAAG,EAAE,UAAU,CAAC,CAAC;IACtD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI,wBAAwB,CAAC;QAC1E,IAAI,SAAS,MAAM;YACjB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;QACrC;QAEA,MAAM,YAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,GAAI,QAAQ,OAAO,IAAI,CAAC,CAAC;YAC3B;QACF;QAEA,IAAI,CAAC,UAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,UAAS,MAAM,CAAC,CAAC,EAAE,UAAS,UAAU,EAAE;YACpF,IAAI;YACJ,IAAI;gBACF,aAAY,MAAM,UAAS,IAAI;YACjC,EAAE,OAAO,GAAG;gBACV,aAAY;oBAAE,SAAS,UAAS,UAAU;gBAAC;YAC7C;YACA,OAAO;gBACL,SAAS;gBACT,WAAW,AAAC,cAAa,YAAW,aAAc;gBAClD,SAAS,YAAW,UAAU,YAAW,WAAW,CAAC,MAAM,EAAE,UAAS,MAAM,CAAC,GAAG,EAAE,UAAS,UAAU,CAAC,kCAAkC,CAAC;YAC3I;QACF;QAEA,IAAI,UAAS,MAAM,KAAK,OAAO,UAAS,MAAM,KAAK,KAAK;YACtD,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,MAAM,OAAO,MAAM,UAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS,CAAC,0DAA0D,EAAE,MAAM,OAAO,IAAI,wBAAwB,CAAC,CAAC;QACnH;IACF;AACF;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC,kBAAkB,oBAAoB;IACzD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,yBAAyB;AACnD;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,eAAe,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD,EAAE,YAAY,GAAG,CAAC,kBAAkB;IACjE,OAAO;AACT;AAEO,eAAe,eAAe,KAAK;IACxC,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAE3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,2EAA2E;QAC3E,0EAA0E;QAC1E,kDAAkD;QAClD,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,KAAK,iCAAiC;YAChF,QAAQ,IAAI,CAAC;QAClB,OAAO,IAAI,MAAM,IAAI,KAAK,cAAc;YACnC,QAAQ,IAAI,CAAC;QACb,2DAA2D;QAC3D,4DAA4D;QACjE,OAAO;YACL,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO,MAAM,kDAAkD;IACjE;AACF;;;IAtNsB;IAiBA;IAMA;IAKA;IAsGA;IAmDA;IAMA;IAMA;;AAjMA,+OAAA;AAiBA,+OAAA;AAMA,+OAAA;AAKA,+OAAA;AAsGA,+OAAA;AAmDA,+OAAA;AAMA,+OAAA;AAMA,+OAAA", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/authenticate.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse } from \"@/lib/apiUtils\";\r\nimport { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from \"@/lib/schemas/authSchema\";\r\nimport { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from \"@/lib/sessionUtils\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport async function registerUser(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = registerSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(validatedFields.data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(\"Registration failed:\", response);\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, formDataObject, errorData?.message || \"Registration failed. Please try again.\");\r\n    }\r\n  } catch (error) {\r\n    return handleErrorResponse(false, formDataObject, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/dang-ki/dang-ki-thanh-cong\");\r\n}\r\n\r\nexport async function loginUser(prevState, formData) {\r\n  const validatedFields = loginSchema.safeParse({\r\n    email: formData.get(\"email\"),\r\n    password: formData.get(\"password\"),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: {\r\n        email: formData.get(\"email\"),\r\n      },\r\n    };\r\n  }\r\n\r\n  let urlCallback = \"/user/profile\";\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n        password: validatedFields.data.password,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, { email: formData.get(\"email\") }, errorData?.message || \"Thông tin đăng nhập không đúng\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const token = data.token;\r\n    const user = {\r\n      id: data.id,\r\n      fullName: data.fullName,\r\n      email: data.email,\r\n      userType: data.userType,\r\n      phone: data.phone,\r\n      lastLogin: data.lastLogin,\r\n    };\r\n\r\n    await createSession(\"Authorization\", token);\r\n    await createSession(\"UserProfile\", JSON.stringify(user));\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n  revalidatePath('/');\r\n\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  \r\n  redirect(urlCallback);\r\n}\r\n\r\nexport async function changePassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = changePasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  const jwtData = await getJwtInfo();\r\n\r\n  let payload = {\r\n    email: jwtData.email,\r\n    oldPassword: validatedFields.data.oldPassword,\r\n    newPassword: validatedFields.data.newPassword,\r\n  };\r\n\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(payload),\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n  });\r\n}\r\n\r\nexport async function forgotPassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/da-gui-email-khoi-phuc-mat-khau\");\r\n}\r\n\r\nexport async function getUserProfile() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);\r\n}\r\n\r\nexport async function validateTokenDirectlyFromAPIServer() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);\r\n}\r\n\r\nexport async function validateTokenServer() {\r\n  const token = await getSession(\"Authorization\");\r\n  if (!token) {\r\n    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };\r\n  }\r\n\r\n  const decoded = await verifyJwtToken(token);\r\n  if (!decoded) {\r\n    deleteSession(\"Authorization\");\r\n    deleteSession(\"UserProfile\");\r\n    return { isLoggedIn: false, isExpired: true};\r\n  }\r\n\r\n  return { isLoggedIn: true, isExpired: false };\r\n}\r\n\r\nexport async function logout() {\r\n  await deleteSession(\"Authorization\");\r\n  await deleteSession(\"UserProfile\");\r\n  redirect(\"/\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;AAEO,eAAe,aAAa,SAAS,EAAE,QAAQ;IACpD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;IAE1D,MAAM,kBAAkB,6HAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;IAEjD,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;YACT,aAAa;QACf;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,gBAAgB,IAAI;QAC3C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;YACpD,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,gBAAgB,WAAW,WAAW;QAC1E;IACF,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,gBAAgB;IACpD;IAEA,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;AAEO,eAAe,UAAU,SAAS,EAAE,QAAQ;IACjD,MAAM,kBAAkB,6HAAA,CAAA,cAAW,CAAC,SAAS,CAAC;QAC5C,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;YACT,aAAa;gBACX,OAAO,SAAS,GAAG,CAAC;YACtB;QACF;IACF;IAEA,IAAI,cAAc;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,gBAAgB,IAAI,CAAC,KAAK;gBACjC,UAAU,gBAAgB,IAAI,CAAC,QAAQ;YACzC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;YACpD,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;gBAAE,OAAO,SAAS,GAAG,CAAC;YAAS,GAAG,WAAW,WAAW;QAC5F;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,OAAO;YACX,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;QAC3B;QAEA,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB;QACrC,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,KAAK,SAAS,CAAC;IACpD,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YAAE,OAAO,SAAS,GAAG,CAAC;QAAS,GAAG;IACtE;IACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;AAEO,eAAe,eAAe,SAAS,EAAE,QAAQ;IACtD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;IAE1D,MAAM,kBAAkB,6HAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC;IAEvD,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IAE/B,IAAI,UAAU;QACZ,OAAO,QAAQ,KAAK;QACpB,aAAa,gBAAgB,IAAI,CAAC,WAAW;QAC7C,aAAa,gBAAgB,IAAI,CAAC,WAAW;IAC/C;IAEA,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE;QACxE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;QACrB,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAEO,eAAe,eAAe,SAAS,EAAE,QAAQ;IACtD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;IAE1D,MAAM,kBAAkB,6HAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC;IAEvD,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;YACT,aAAa;QACf;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,gBAAgB,IAAI,CAAC,KAAK;YACnC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YAAE,OAAO,SAAS,GAAG,CAAC;QAAS,GAAG;IACtE;IAEA,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;AACjE;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,wBAAwB,CAAC;AAC7E;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;IAC/B,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,YAAY;YAAO,WAAW;YAAO,WAAW;QAAW;IACtE;IAEA,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;IACrC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;QACd,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;QACd,OAAO;YAAE,YAAY;YAAO,WAAW;QAAI;IAC7C;IAEA,OAAO;QAAE,YAAY;QAAM,WAAW;IAAM;AAC9C;AAEO,eAAe;IACpB,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;IACpB,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;IACpB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;;;IApLsB;IAkCA;IA0DA;IA8BA;IA8BA;IAIA;IAIA;IAgBA;;AAhLA,+OAAA;AAkCA,+OAAA;AA0DA,+OAAA;AA8BA,+OAAA;AA8BA,+OAAA;AAIA,+OAAA;AAIA,+OAAA;AAgBA,+OAAA", "debugId": null}}, {"offset": {"line": 1549, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/layout.jsx"], "sourcesContent": ["import { Inter } from \"next/font/google\";\r\nimport \"../globals.css\";\r\nimport Footer from \"@/components/layout/Footer\";\r\nimport Navbar from \"@/components/layout/Navbar\";\r\nimport AlertPopup from \"@/components/layout/AlertPopup\";\r\nimport ProfileProvider from \"@/components/layout/ProfileProvider\";\r\nimport { AlertProvider } from \"@/contexts/AlertContext\";\r\nimport { Toaster } from \"@/components/ui/toaster\";\r\nimport { NextIntlClientProvider, hasLocale } from \"next-intl\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { routing } from \"@/i18n/routing\";\r\nimport { cookies } from \"next/headers\";\r\nimport { getMessages } from \"next-intl/server\";\r\nimport { AuthProvider } from \"@/contexts/AuthContext\";\r\nimport { validateTokenServer } from \"../actions/server/authenticate\";\r\n\r\nconst inter = Inter({\r\n  variable: \"--font-inter\",\r\n  weight: [\"400\", \"700\"],\r\n  subsets: [\"vietnamese\"],\r\n});\r\n\r\nexport const metadata = {\r\n  title: \"YEZHOME\",\r\n  description: \"YEZHOME: <PERSON><PERSON><PERSON> n<PERSON>i <PERSON> chón<PERSON>, giao dịch tin cậy.\",\r\n};\r\n\r\nexport default async function RootLayout({ children, params }) {\r\n  // Ensure that the incoming `locale` is valid\r\n  const { locale } = await params;\r\n  if (!hasLocale(routing.locales, locale)) {\r\n    notFound();\r\n  }\r\n\r\n  const messages = await getMessages();\r\n\r\n  // Check if user is logged in\r\n  const initialAuthState = await validateTokenServer();\r\n  const isLoggedIn = initialAuthState.isLoggedIn;\r\n\r\n  let userData = null;\r\n  if (isLoggedIn) {\r\n    const userCookie = (await cookies()).get(\"UserProfile\");\r\n    if (userCookie) {\r\n      try {\r\n        userData = JSON.parse(userCookie.value);\r\n      } catch (e) {\r\n        console.error(\"Failed to parse user data from cookie:\", e);\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <html lang={locale}>\r\n      <body className={`${inter.variable} antialiased`} suppressHydrationWarning>\r\n        <NextIntlClientProvider locale={locale} messages={messages}>\r\n          <Toaster />\r\n          <AlertProvider>\r\n            <AuthProvider initialAuthState={initialAuthState}>\r\n              {/* Only wrap with ProfileProvider if user is logged in */}\r\n              {isLoggedIn ? (\r\n                <ProfileProvider initialUserData={userData} isLoggedIn={isLoggedIn}>\r\n                  <div className=\"min-h-screen [--header-height:calc(theme(spacing.14))]\">\r\n                    <Navbar isLoggedIn={isLoggedIn}></Navbar>\r\n                    <div className=\"relative\">{children}</div>\r\n                  </div>\r\n                  <Footer></Footer>\r\n                  <AlertPopup></AlertPopup>\r\n                </ProfileProvider>\r\n              ) : (\r\n                <>\r\n                  <div className=\"min-h-screen [--header-height:calc(theme(spacing.14))]\">\r\n                    <Navbar isLoggedIn={isLoggedIn}></Navbar>\r\n                    <div className=\"relative\">{children}</div>\r\n                  </div>\r\n                  <Footer></Footer>\r\n                  <AlertPopup></AlertPopup>\r\n                </>\r\n              )}\r\n            </AuthProvider>\r\n          </AlertProvider>\r\n        </NextIntlClientProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAQO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,eAAe,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE;IAC3D,6CAA6C;IAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,IAAI,CAAC,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,+GAAA,CAAA,UAAO,CAAC,OAAO,EAAE,SAAS;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,6BAA6B;IAC7B,MAAM,mBAAmB,MAAM,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD;IACjD,MAAM,aAAa,iBAAiB,UAAU;IAE9C,IAAI,WAAW;IACf,IAAI,YAAY;QACd,MAAM,aAAa,CAAC,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC;QACzC,IAAI,YAAY;YACd,IAAI;gBACF,WAAW,KAAK,KAAK,CAAC,WAAW,KAAK;YACxC,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;IACF;IAEA,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,wBAAwB;sBACxE,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,QAAQ;gBAAQ,UAAU;;kCAChD,8OAAC,4HAAA,CAAA,UAAO;;;;;kCACR,8OAAC,yHAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,wHAAA,CAAA,eAAY;4BAAC,kBAAkB;sCAE7B,2BACC,8OAAC,wIAAA,CAAA,UAAe;gCAAC,iBAAiB;gCAAU,YAAY;;kDACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,+HAAA,CAAA,UAAM;gDAAC,YAAY;;;;;;0DACpB,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE7B,8OAAC,+HAAA,CAAA,UAAM;;;;;kDACP,8OAAC,mIAAA,CAAA,UAAU;;;;;;;;;;qDAGb;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,+HAAA,CAAA,UAAM;gDAAC,YAAY;;;;;;0DACpB,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE7B,8OAAC,+HAAA,CAAA,UAAM;;;;;kDACP,8OAAC,mIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B", "debugId": null}}]}
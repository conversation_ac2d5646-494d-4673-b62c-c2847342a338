module.exports = {

"[project]/components/property/SearchFilter.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/components_25beebf6._.js",
  "server/chunks/ssr/node_modules_2dffe111._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/SearchFilter.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyList.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_889dbbf9._.js",
  "server/chunks/ssr/node_modules_0dd9ed99._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyList.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};
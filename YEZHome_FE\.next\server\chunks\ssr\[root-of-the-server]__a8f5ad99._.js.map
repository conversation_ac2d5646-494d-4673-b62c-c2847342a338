{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/i18n/routing.ts"], "sourcesContent": ["import {defineRouting} from 'next-intl/routing';\r\n \r\nexport const routing = defineRouting({\r\n  // A list of all locales that are supported\r\n  locales: ['en', 'vi'],\r\n \r\n  // Used when no locale matches\r\n  defaultLocale: 'vi'\r\n});"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/i18n/navigation.ts"], "sourcesContent": ["import {createNavigation} from 'next-intl/navigation';\r\nimport {routing} from './routing';\r\n \r\n// Lightweight wrappers around Next.js' navigation\r\n// APIs that consider the routing configuration\r\nexport const {Link, redirect, usePathname, useRouter, getPathname} =\r\n  createNavigation(routing);"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAIO,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAC,GAChE,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE,+GAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function parseEmptyStringsToNull(payload) {\r\n  if (Array.isArray(payload)) {\r\n    return payload.map(item => parseEmptyStringsToNull(item));\r\n  }\r\n\r\n  if (typeof payload === 'object' && payload !== null) {\r\n    const newPayload = { ...payload };\r\n\r\n    Object.keys(newPayload).forEach(key => {\r\n      if (newPayload[key] === '') {\r\n        newPayload[key] = null;\r\n      } else if (typeof newPayload[key] === 'object' && newPayload[key] !== null) {\r\n        newPayload[key] = parseEmptyStringsToNull(newPayload[key]);\r\n      }\r\n    });\r\n\r\n    return newPayload;\r\n  }\r\n\r\n  return payload;\r\n}\r\n\r\nexport function formatCurrency(amount) {\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'currency',\r\n    currency: 'VND',\r\n    maximumFractionDigits: 0\r\n  }).format(amount);\r\n}\r\n\r\nexport function formatDate(dateString) {\r\n  return new Date(dateString).toLocaleDateString('vi-VN', {\r\n    day: '2-digit',\r\n    month: '2-digit',\r\n    year: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n}\r\n\r\nexport const formatPriceShort = (price) => {\r\n  if (price === null || price === undefined) return 'N/A';\r\n  if (price >= 1000000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên tỷ\r\n      const val = (price / 1000000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tỷ' : val + ' Tỷ';\r\n  }\r\n  if (price >= 1000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên triệu\r\n       const val = (price / 1000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tr' : val + ' Tr';\r\n  }\r\n   // Định dạng số thông thường cho các giá trị nhỏ hơn 1 triệu\r\n   if (typeof price === 'number') {\r\n       return price.toLocaleString('vi-VN');\r\n   }\r\n  return String(price); // Trường hợp khác cố gắng convert sang string\r\n};\r\n\r\nexport function debounce(func, delay) {\r\n  let timeoutId;\r\n  // Hàm debounce trả về một hàm mới\r\n  const debounced = function(...args) {\r\n    const context = this; // Lưu ngữ cảnh 'this'\r\n    clearTimeout(timeoutId); // Xóa timer cũ nếu có\r\n    // Thiết lập timer mới để gọi hàm gốc sau độ trễ\r\n    timeoutId = setTimeout(() => {\r\n      func.apply(context, args); // Gọi hàm gốc với ngữ cảnh và đối số đúng\r\n    }, delay);\r\n  };\r\n\r\n  // Thêm phương thức cancel vào hàm debounced trả về\r\n  debounced.cancel = function() {\r\n    clearTimeout(timeoutId);\r\n  };\r\n\r\n  return debounced; // Trả về hàm đã được debounce\r\n}"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,wBAAwB,OAAO;IAC7C,IAAI,MAAM,OAAO,CAAC,UAAU;QAC1B,OAAO,QAAQ,GAAG,CAAC,CAAA,OAAQ,wBAAwB;IACrD;IAEA,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAEhC,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC9B,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI;gBAC1B,UAAU,CAAC,IAAI,GAAG;YACpB,OAAO,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,YAAY,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC1E,UAAU,CAAC,IAAI,GAAG,wBAAwB,UAAU,CAAC,IAAI;YAC3D;QACF;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,MAAM;IACnC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,UAAU;IACnC,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,SAAS,YAAY;QACrB,8DAA8D;QAC9D,MAAM,MAAM,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC;QACzC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACA,IAAI,SAAS,SAAS;QAClB,iEAAiE;QAChE,MAAM,MAAM,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC;QACvC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACC,4DAA4D;IAC5D,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,MAAM,cAAc,CAAC;IAChC;IACD,OAAO,OAAO,QAAQ,8CAA8C;AACtE;AAEO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,kCAAkC;IAClC,MAAM,YAAY,SAAS,GAAG,IAAI;QAChC,MAAM,UAAU,IAAI,EAAE,sBAAsB;QAC5C,aAAa,YAAY,sBAAsB;QAC/C,gDAAgD;QAChD,YAAY,WAAW;YACrB,KAAK,KAAK,CAAC,SAAS,OAAO,0CAA0C;QACvE,GAAG;IACL;IAEA,mDAAmD;IACnD,UAAU,MAAM,GAAG;QACjB,aAAa;IACf;IAEA,OAAO,WAAW,8BAA8B;AAClD", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/avatar.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className)}\r\n    {...props} />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props} />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxD,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAEb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAEb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/sheet.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva } from \"class-variance-authority\";\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref} />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\nconst SheetContent = React.forwardRef(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content ref={ref} className={cn(sheetVariants({ side }), className)} {...props}>\r\n      <SheetPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n      {children}\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-2 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props} />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;AACA;AACA;AACA;AAEA;AANA;;;;;;;AAQA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAET,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAGF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACxF,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBAAC,KAAK;gBAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBAAa,GAAG,KAAK;;kCAC5F,8OAAC,kKAAA,CAAA,QAAoB;wBACnB,WAAU;;0CACV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;oBAE3B;;;;;;;;;;;;;AAIP,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;QACjE,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/dropdown-menu.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}>\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className)}\r\n    {...props} />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props} />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}) => {\r\n  return (\r\n    (<span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzF,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YACR;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAEb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBACrF,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAGf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAEb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7F,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BACT,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjF,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BACT,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC1E,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC,SAAS,QAAQ;QACnE,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAEb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,8OAAC;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAEf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/tooltip.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </TooltipPrimitive.Portal>\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAChF,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qXACA;YAED,GAAG,KAAK;;;;;;;;;;;AAGf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/authenticate.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse } from \"@/lib/apiUtils\";\r\nimport { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from \"@/lib/schemas/authSchema\";\r\nimport { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from \"@/lib/sessionUtils\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\nexport async function registerUser(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = registerSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(validatedFields.data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(\"Registration failed:\", response);\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, formDataObject, errorData?.message || \"Registration failed. Please try again.\");\r\n    }\r\n  } catch (error) {\r\n    return handleErrorResponse(false, formDataObject, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/dang-ki/dang-ki-thanh-cong\");\r\n}\r\n\r\nexport async function loginUser(prevState, formData) {\r\n  const validatedFields = loginSchema.safeParse({\r\n    email: formData.get(\"email\"),\r\n    password: formData.get(\"password\"),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: {\r\n        email: formData.get(\"email\"),\r\n      },\r\n    };\r\n  }\r\n\r\n  let urlCallback = \"/user/profile\";\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n        password: validatedFields.data.password,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, { email: formData.get(\"email\") }, errorData?.message || \"Thông tin đăng nhập không đúng\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const token = data.token;\r\n    const user = {\r\n      id: data.id,\r\n      fullName: data.fullName,\r\n      email: data.email,\r\n      userType: data.userType,\r\n      phone: data.phone,\r\n      lastLogin: data.lastLogin,\r\n    };\r\n\r\n    await createSession(\"Authorization\", token);\r\n    await createSession(\"UserProfile\", JSON.stringify(user));\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n  revalidatePath('/');\r\n  redirect(urlCallback);\r\n}\r\n\r\nexport async function changePassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = changePasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  const jwtData = await getJwtInfo();\r\n\r\n  let payload = {\r\n    email: jwtData.email,\r\n    oldPassword: validatedFields.data.oldPassword,\r\n    newPassword: validatedFields.data.newPassword,\r\n  };\r\n\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(payload),\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n  });\r\n}\r\n\r\nexport async function forgotPassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/da-gui-email-khoi-phuc-mat-khau\");\r\n}\r\n\r\nexport async function getUserProfile() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);\r\n}\r\n\r\nexport async function validateTokenDirectlyFromAPIServer() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);\r\n}\r\n\r\nexport async function validateTokenServer() {\r\n  const token = await getSession(\"Authorization\");\r\n  if (!token) {\r\n    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };\r\n  }\r\n\r\n  const decoded = await verifyJwtToken(token);\r\n  if (!decoded) {\r\n    deleteSession(\"Authorization\");\r\n    deleteSession(\"UserProfile\");\r\n    return { isLoggedIn: false, isExpired: true};\r\n  }\r\n\r\n  return { isLoggedIn: true, isExpired: false };\r\n}\r\n\r\nexport async function logout() {\r\n  await deleteSession(\"Authorization\");\r\n  await deleteSession(\"UserProfile\");\r\n  redirect(\"/\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoLsB,SAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/ButtonLogOut.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { logout } from '@/app/actions/server/authenticate';\r\nimport { LogOutIcon } from 'lucide-react';\r\nexport default function ButtonLogOut() {\r\n  return (\r\n    <button\r\n      className=\"flex items-center gap-3 rounded-lg py-2 text-sm font-medium text-red-600 transition-all hover:text-red-900 w-full\"\r\n      onClick={async () => {\r\n        try {\r\n          await logout();\r\n        } catch (error) {\r\n          console.error('Error logging out:', error);\r\n        }\r\n      }}\r\n    >\r\n      <LogOutIcon className=\"h-4 w-4\" />\r\n      Đ<PERSON>ng xuất\r\n    </button>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;YACP,IAAI;gBACF,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;;0BAEA,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAAY;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Button = React.forwardRef(({ className, variant, size, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  return (\r\n    <Comp\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      ref={ref}\r\n      {...props} />\r\n  );\r\n})\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxF,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;AACA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/common/NavBarData.jsx"], "sourcesContent": ["import { Chart<PERSON>ie, Layers, MessageSquareDot, Settings, User } from \"lucide-react\";\r\n\r\nexport const NavBarData = [\r\n  {\r\n    id: \"home\",\r\n    name: \"Trang chủ\",\r\n    url: \"/\",\r\n    subnav: [],\r\n  },\r\n  {\r\n    id: \"post\",\r\n    name: \"<PERSON> tức\",\r\n    url: \"/tin-tuc\",\r\n    subnav: [],\r\n  },\r\n  {\r\n    id: \"about\",\r\n    name: \"<PERSON>iớ<PERSON> thiệu\",\r\n    url: \"/gioi-thieu\",\r\n    subnav: [],\r\n  },\r\n  {\r\n    id: \"contact\",\r\n    name: \"<PERSON><PERSON><PERSON> hệ\",\r\n    url: \"/lien-he\",\r\n    subnav: [],\r\n  },\r\n];\r\n\r\nexport const sidebarMenuItems = [\r\n  { href: \"/user/dashboard\", label: \"Tổng quan\", icon: Chart<PERSON>ie },\r\n  { href: \"/user/profile\", label: \"Thông tin cá nhân\", icon: User },\r\n  { href: \"/user/bds\", label: \"BĐS của tôi\", icon: Layers },\r\n  { href: \"/user/notifications\", label: \"Thông báo\", icon: MessageSquareDot },\r\n  { href: \"/user/setting\", label: \"Ti<PERSON>n ích\", icon: Settings },\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEO,MAAM,aAAa;IACxB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,QAAQ,EAAE;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,QAAQ,EAAE;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,QAAQ,EAAE;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,QAAQ,EAAE;IACZ;CACD;AAEM,MAAM,mBAAmB;IAC9B;QAAE,MAAM;QAAmB,OAAO;QAAa,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAiB,OAAO;QAAqB,MAAM,kMAAA,CAAA,OAAI;IAAC;IAChE;QAAE,MAAM;QAAa,OAAO;QAAe,MAAM,sMAAA,CAAA,SAAM;IAAC;IACxD;QAAE,MAAM;QAAuB,OAAO;QAAa,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAC1E;QAAE,MAAM;QAAiB,OAAO;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC5D", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4DsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/LanguageSwitcher.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from \"react\";\r\nimport { useLocale } from \"next-intl\";\r\nimport { createNavigation } from \"next-intl/navigation\";\r\nimport { Globe } from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { routing } from \"@/i18n/routing\";\r\n\r\nexport function LanguageSwitcher() {\r\n  const { usePathname, useRouter } = createNavigation(routing);\r\n  const pathname = usePathname();\r\n  const locale = useLocale();\r\n  const router = useRouter();\r\n\r\n  const changeLocale = (newLocale) => {\r\n    // Use replace to avoid adding a new entry to the history stack\r\n    router.replace(pathname, { locale: newLocale });\r\n  };\r\n\r\n  // Get the current language name\r\n  const getCurrentLanguageName = () => {\r\n    return locale === 'en' ? 'English' : 'Tiếng Việt';\r\n  };\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\">\r\n          <Globe/> {getCurrentLanguageName()}\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem\r\n          onClick={() => changeLocale('en')}\r\n          disabled={locale === 'en'}\r\n        >\r\n          English\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem\r\n          onClick={() => changeLocale('vi')}\r\n          disabled={locale === 'vi'}\r\n        >\r\n          Tiếng Việt\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAMA;AAdA;;;;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE,+GAAA,CAAA,UAAO;IAC3D,MAAM,WAAW;IACjB,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS;IAEf,MAAM,eAAe,CAAC;QACpB,+DAA+D;QAC/D,OAAO,OAAO,CAAC,UAAU;YAAE,QAAQ;QAAU;IAC/C;IAEA,gCAAgC;IAChC,MAAM,yBAAyB;QAC7B,OAAO,WAAW,OAAO,YAAY;IACvC;IAEA,qBACE,8OAAC,qIAAA,CAAA,eAAY;;0BACX,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;;sCACd,8OAAC,oMAAA,CAAA,QAAK;;;;;wBAAE;wBAAE;;;;;;;;;;;;0BAGd,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,qIAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,aAAa;wBAC5B,UAAU,WAAW;kCACtB;;;;;;kCAGD,8OAAC,qIAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,aAAa;wBAC5B,UAAU,WAAW;kCACtB;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/popover.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor\r\n\r\nconst PopoverContent = React.forwardRef(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,gBAAgB,mKAAA,CAAA,SAAuB;AAE7C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAClG,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAGf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/notification.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/notifications`;\r\n\r\n/**\r\n * Get notifications with pagination and optional type filtering\r\n * @param {Object} params - Query parameters\r\n * @param {number} params.page - Page number (default: 1)\r\n * @param {number} params.limit - Items per page (default: 10)\r\n * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getNotifications(params = {}) {\r\n  try {\r\n    const { page = 1, limit = 10, type } = params;\r\n    \r\n    // If type is specified, use the by-type endpoint\r\n    let url = type \r\n      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`\r\n      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;\r\n    \r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getNotifications\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get unread notification count\r\n * @returns {Promise<Object>} Response with notification count data\r\n */\r\nexport async function getUnreadCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getUnreadCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số thông báo chưa đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark notifications as read\r\n * @param {Object} params - Request parameters\r\n * @param {Array<string>} params.ids - Array of notification IDs to mark as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAsRead(params) {\r\n  try {\r\n    // The API expects marking one notification at a time with a specific endpoint\r\n    if (params.ids && params.ids.length > 0) {\r\n      // Mark the first notification in the array\r\n      const id = params.ids[0];\r\n      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {\r\n        method: \"PUT\", // Changed from POST to PUT as per API doc\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    }\r\n    return handleErrorResponse(false, null, \"Không có ID thông báo được cung cấp\");\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAsRead\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark all notifications as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAllAsRead() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {\r\n      method: \"PUT\", // Changed from POST to PUT as per API doc\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAllAsRead\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get latest notifications (for navbar dropdown)\r\n * @param {number} limit - Number of notifications to get\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getLatestNotifications(limit = 6) {\r\n  try {\r\n    // Using the default endpoint with a small page size for latest notifications\r\n    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getLatestNotifications\",\r\n      limit,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo mới nhất\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;IAiHsB,yBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/notification.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/notifications`;\r\n\r\n/**\r\n * Get notifications with pagination and optional type filtering\r\n * @param {Object} params - Query parameters\r\n * @param {number} params.page - Page number (default: 1)\r\n * @param {number} params.limit - Items per page (default: 10)\r\n * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getNotifications(params = {}) {\r\n  try {\r\n    const { page = 1, limit = 10, type } = params;\r\n    \r\n    // If type is specified, use the by-type endpoint\r\n    let url = type \r\n      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`\r\n      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;\r\n    \r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getNotifications\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get unread notification count\r\n * @returns {Promise<Object>} Response with notification count data\r\n */\r\nexport async function getUnreadCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getUnreadCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số thông báo chưa đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark notifications as read\r\n * @param {Object} params - Request parameters\r\n * @param {Array<string>} params.ids - Array of notification IDs to mark as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAsRead(params) {\r\n  try {\r\n    // The API expects marking one notification at a time with a specific endpoint\r\n    if (params.ids && params.ids.length > 0) {\r\n      // Mark the first notification in the array\r\n      const id = params.ids[0];\r\n      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {\r\n        method: \"PUT\", // Changed from POST to PUT as per API doc\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    }\r\n    return handleErrorResponse(false, null, \"Không có ID thông báo được cung cấp\");\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAsRead\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark all notifications as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAllAsRead() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {\r\n      method: \"PUT\", // Changed from POST to PUT as per API doc\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAllAsRead\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get latest notifications (for navbar dropdown)\r\n * @param {number} limit - Number of notifications to get\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getLatestNotifications(limit = 6) {\r\n  try {\r\n    // Using the default endpoint with a small page size for latest notifications\r\n    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getLatestNotifications\",\r\n      limit,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo mới nhất\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;IA2CsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/notification.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/notifications`;\r\n\r\n/**\r\n * Get notifications with pagination and optional type filtering\r\n * @param {Object} params - Query parameters\r\n * @param {number} params.page - Page number (default: 1)\r\n * @param {number} params.limit - Items per page (default: 10)\r\n * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getNotifications(params = {}) {\r\n  try {\r\n    const { page = 1, limit = 10, type } = params;\r\n    \r\n    // If type is specified, use the by-type endpoint\r\n    let url = type \r\n      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`\r\n      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;\r\n    \r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getNotifications\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get unread notification count\r\n * @returns {Promise<Object>} Response with notification count data\r\n */\r\nexport async function getUnreadCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getUnreadCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số thông báo chưa đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark notifications as read\r\n * @param {Object} params - Request parameters\r\n * @param {Array<string>} params.ids - Array of notification IDs to mark as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAsRead(params) {\r\n  try {\r\n    // The API expects marking one notification at a time with a specific endpoint\r\n    if (params.ids && params.ids.length > 0) {\r\n      // Mark the first notification in the array\r\n      const id = params.ids[0];\r\n      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {\r\n        method: \"PUT\", // Changed from POST to PUT as per API doc\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    }\r\n    return handleErrorResponse(false, null, \"Không có ID thông báo được cung cấp\");\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAsRead\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark all notifications as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAllAsRead() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {\r\n      method: \"PUT\", // Changed from POST to PUT as per API doc\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAllAsRead\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get latest notifications (for navbar dropdown)\r\n * @param {number} limit - Number of notifications to get\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getLatestNotifications(limit = 6) {\r\n  try {\r\n    // Using the default endpoint with a small page size for latest notifications\r\n    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getLatestNotifications\",\r\n      limit,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo mới nhất\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;IAiEsB,aAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/icon/atm-icon.tsx"], "sourcesContent": ["export default function AtmIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className}><path d=\"M27.2667 25H4.73333C3.77653 25 3 24.2669 3 23.3636V8.63636C3 7.73309 3.77653 7 4.73333 7H27.2667C28.2235 7 29 7.73309 29 8.63636V23.3636C29 24.2669 28.2235 25 27.2667 25Z\" fill=\"#999999\"></path><rect x=\"3\" y=\"11\" width=\"26\" height=\"3\" fill=\"#F2F2F2\"></rect><path d=\"M15.2174 20.8359H13.2972L12.8954 22H12L13.8726 17H14.6454L16.5214 22H15.6226L15.2174 20.8359ZM13.5389 20.1353H14.9757L14.2573 18.0611L13.5389 20.1353Z\" fill=\"#FFFFFF\"></path><path d=\"M20.2597 17.7005H18.714V22H17.8594V17.7005H16.3273V17H20.2597V17.7005Z\" fill=\"#FFFFFF\"></path><path d=\"M22.0302 17L23.4601 20.8324L24.8867 17H26V22H25.142V20.3516L25.2271 18.147L23.7631 22H23.1469L21.6863 18.1504L21.7714 20.3516V22H20.9134V17H22.0302Z\" fill=\"#FFFFFF\"></path></svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,QAAQ,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BAAW,8OAAC;gBAAK,GAAE;gBAA6K,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAK,QAAO;gBAAI,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAyJ,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAyE,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAuJ,MAAK;;;;;;;;;;;;AAEr0B", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/icon/banking-icon.tsx"], "sourcesContent": ["export default function BankingIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className}><path d=\"M8.97908 13.2568H5.71802V27.4138H8.97908V13.2568Z\" fill=\"#1DBABF\"></path><path d=\"M14.4139 13.2568H11.1528V27.4138H14.4139V13.2568Z\" fill=\"#1DBABF\"></path><path d=\"M19.8494 13.2568H16.5884V27.4138H19.8494V13.2568Z\" fill=\"#1DBABF\"></path><path d=\"M25.2843 13.2568H22.0232V27.4138H25.2843V13.2568Z\" fill=\"#1DBABF\"></path><path d=\"M27.7186 10.6005L15.7613 4.06653C15.6815 4.02288 15.592 4 15.501 4C15.41 4 15.3205 4.02288 15.2407 4.06653L3.28341 10.6005C3.1977 10.6473 3.12617 10.7165 3.07634 10.8006C3.02651 10.8847 3.00023 10.9807 3.00024 11.0786V14.3455C3.00024 14.49 3.05751 14.6284 3.15943 14.7306C3.26136 14.8327 3.39961 14.89 3.54375 14.89H27.4582C27.6024 14.89 27.7406 14.8327 27.8426 14.7306C27.9445 14.6284 28.0017 14.49 28.0017 14.3455V11.0786C28.0018 10.9807 27.9755 10.8847 27.9256 10.8006C27.8758 10.7165 27.8043 10.6473 27.7186 10.6005Z\" fill=\"#009BA1\"></path><path d=\"M27.4582 25.7803H3.54375C3.39961 25.7803 3.26136 25.8376 3.15943 25.9398C3.05751 26.0419 3.00024 26.1804 3.00024 26.3248V27.4138C3.00024 27.5582 3.05751 27.6967 3.15943 27.7988C3.26136 27.9009 3.39961 27.9583 3.54375 27.9583H27.4582C27.6024 27.9583 27.7406 27.9009 27.8426 27.7988C27.9445 27.6967 28.0017 27.5582 28.0017 27.4138V26.3248C28.0017 26.1804 27.9445 26.0419 27.8426 25.9398C27.7406 25.8376 27.6024 25.7803 27.4582 25.7803Z\" fill=\"#009BA1\"></path><path d=\"M15.5007 12.1675C16.7014 12.1675 17.6747 11.1924 17.6747 9.98951C17.6747 8.78664 16.7014 7.81152 15.5007 7.81152C14.3 7.81152 13.3267 8.78664 13.3267 9.98951C13.3267 11.1924 14.3 12.1675 15.5007 12.1675Z\" fill=\"#FFFFFF\"></path></svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,YAAY,EAAE,SAAS,EAA0B;IACvE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BAAW,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAA2gB,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAob,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAA8M,MAAK;;;;;;;;;;;;AAEvpD", "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/icon/creditcard-icon.tsx"], "sourcesContent": ["export default function CreditCardIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className}><path d=\"M27.2667 25H4.73333C3.77653 25 3 24.2669 3 23.3636V8.63636C3 7.73309 3.77653 7 4.73333 7H27.2667C28.2235 7 29 7.73309 29 8.63636V23.3636C29 24.2669 28.2235 25 27.2667 25Z\" fill=\"#999999\"></path><rect x=\"3\" y=\"11\" width=\"26\" height=\"3\" fill=\"#FFFFFF\"></rect><path opacity=\"0.9\" d=\"M24 23C25.6569 23 27 21.6569 27 20C27 18.3431 25.6569 17 24 17C22.3431 17 21 18.3431 21 20C21 21.6569 22.3431 23 24 23Z\" fill=\"#FFFFFF\"></path><path opacity=\"0.9\" d=\"M20 23C21.6569 23 23 21.6569 23 20C23 18.3431 21.6569 17 20 17C18.3431 17 17 18.3431 17 20C17 21.6569 18.3431 23 20 23Z\" fill=\"#FFFFFF\"></path></svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,eAAe,EAAE,SAAS,EAA0B;IAC1E,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BAAW,8OAAC;gBAAK,GAAE;gBAA6K,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAK,QAAO;gBAAI,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAA0H,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAA0H,MAAK;;;;;;;;;;;;AAE9rB", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/icon/momo-icon.tsx"], "sourcesContent": ["export default function MomoIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className={className}><path d=\"M25.7338 3H6.26625C4.46235 3 3 4.61363 3 6.60414V25.3959C3 27.3864 4.46235 29 6.26625 29H25.7338C27.5377 29 29 27.3864 29 25.3959V6.60414C29 4.61363 27.5377 3 25.7338 3Z\" fill=\"#999999\"></path><path d=\"M21.7314 7.00068C19.3765 7.00068 17.4677 8.81889 17.4677 11.0595C17.4677 13.3002 19.3815 15.1184 21.7314 15.1184C24.0812 15.1184 26 13.3002 26 11.0595C26 8.81889 24.0912 7.00068 21.7314 7.00068ZM21.7314 12.7913C21.2599 12.7999 20.8043 12.6226 20.4646 12.2983C20.1249 11.9741 19.9289 11.5294 19.9198 11.062C19.9441 10.602 20.1456 10.1687 20.4827 9.85166C20.8197 9.53457 21.2667 9.3578 21.7314 9.3578C22.1961 9.3578 22.643 9.53457 22.9801 9.85166C23.3172 10.1687 23.5186 10.602 23.543 11.062C23.5338 11.5294 23.3379 11.9741 22.9982 12.2983C22.6585 12.6226 22.2029 12.7999 21.7314 12.7913ZM16.2492 10.0491V15.1357H13.7872V10.0244C13.7872 9.83444 13.7111 9.65222 13.5755 9.51786C13.44 9.38351 13.2562 9.30803 13.0645 9.30803C12.8729 9.30803 12.6891 9.38351 12.5535 9.51786C12.418 9.65222 12.3419 9.83444 12.3419 10.0244V15.1357H9.88986V10.0244C9.88986 9.83444 9.81372 9.65222 9.6782 9.51786C9.54268 9.38351 9.35887 9.30803 9.16721 9.30803C8.97555 9.30803 8.79174 9.38351 8.65622 9.51786C8.52069 9.65222 8.44456 9.83444 8.44456 10.0244V15.1357H6V10.0491C6.01832 9.22442 6.36561 8.4405 6.96579 7.86911C7.56597 7.29772 8.37012 6.98544 9.20209 7.00068C9.89083 6.99928 10.5624 7.21371 11.1209 7.61334C11.6789 7.21466 12.3493 7.0003 13.0371 7.00068C13.8706 6.98345 14.677 7.29477 15.2792 7.8663C15.8814 8.43783 16.2302 9.22289 16.2492 10.0491ZM21.7314 16.865C19.3765 16.865 17.4677 18.6807 17.4677 20.9238C17.4677 23.167 19.3815 24.9901 21.7314 24.9901C24.0812 24.9901 25.995 23.1744 25.995 20.9337C25.995 18.6931 24.0912 16.865 21.7314 16.865ZM21.7314 22.6556C21.3896 22.6425 21.0592 22.5301 20.7813 22.3323C20.5034 22.1345 20.2903 21.8601 20.1686 21.5432C20.0468 21.2263 20.0217 20.8808 20.0964 20.5499C20.1712 20.219 20.3424 19.9171 20.5888 19.6819C20.8352 19.4467 21.1459 19.2885 21.4823 19.227C21.8187 19.1655 22.166 19.2034 22.4808 19.336C22.7956 19.4687 23.0642 19.6901 23.253 19.9729C23.4419 20.2556 23.5427 20.5872 23.543 20.9263C23.5397 21.1586 23.4903 21.3879 23.3974 21.6012C23.3045 21.8145 23.17 22.0074 23.0017 22.169C22.8333 22.3306 22.6345 22.4577 22.4165 22.5429C22.1985 22.6281 21.9657 22.6697 21.7314 22.6655V22.6556ZM16.2492 19.9233V25H13.7872V19.8962C13.7872 19.7062 13.7111 19.5239 13.5755 19.3896C13.44 19.2552 13.2562 19.1797 13.0645 19.1797C12.8729 19.1797 12.6891 19.2552 12.5535 19.3896C12.418 19.5239 12.3419 19.7062 12.3419 19.8962V25H9.88986V19.8962C9.88986 19.7062 9.81372 19.5239 9.6782 19.3896C9.54268 19.2552 9.35887 19.1797 9.16721 19.1797C8.97555 19.1797 8.79174 19.2552 8.65622 19.3896C8.52069 19.5239 8.44456 19.7062 8.44456 19.8962V25H6V19.9233C6.00746 19.514 6.09622 19.1102 6.26122 18.7349C6.42622 18.3596 6.66422 18.0202 6.96161 17.7362C7.259 17.4521 7.60995 17.229 7.99439 17.0795C8.37883 16.93 8.78922 16.8571 9.20209 16.865C9.8904 16.8639 10.5616 17.0773 11.1209 17.4752C11.6789 17.0769 12.3495 16.8634 13.0371 16.865C13.4507 16.8561 13.8621 16.9283 14.2475 17.0773C14.633 17.2263 14.985 17.4493 15.2833 17.7334C15.5817 18.0175 15.8206 18.3571 15.9864 18.7329C16.1521 19.1087 16.2414 19.5133 16.2492 19.9233Z\" fill=\"white\"></path></svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,SAAS,EAAE,SAAS,EAA0B;IACpE,qBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;QAA6B,WAAW;;0BAAW,8OAAC;gBAAK,GAAE;gBAA4K,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAs+F,MAAK;;;;;;;;;;;;AAEtzG", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/icon/vnpay-icon.tsx"], "sourcesContent": ["export default function VnPayIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className}><path d=\"M24.3074 22.4922H22.4908V24.3088H24.3074V22.4922Z\" fill=\"#999999\"></path><path d=\"M18.8578 17.042H17.0413V18.8586H18.8578V17.042Z\" fill=\"#999999\"></path><path d=\"M20.6744 18.8584H18.8578V20.675H20.6744V18.8584Z\" fill=\"#999999\"></path><path d=\"M18.8577 20.6748H17.0411V22.4914H18.8577V20.6748Z\" fill=\"#999999\"></path><path d=\"M20.6744 22.4922H18.8578V24.3088H20.6744V22.4922Z\" fill=\"#999999\"></path><path d=\"M22.491 20.6748H20.6744V22.4914H22.491V20.6748Z\" fill=\"#999999\"></path><path d=\"M22.491 17.042H20.6744V18.8586H22.491V17.042Z\" fill=\"#999999\"></path><path d=\"M24.3074 18.8584H22.4908V20.675H24.3074V18.8584Z\" fill=\"#999999\"></path><rect x=\"8.61536\" y=\"8.61523\" width=\"5.53846\" height=\"5.53846\" stroke=\"#999999\" strokeWidth=\"1.7\" strokeLinejoin=\"round\"></rect><rect x=\"8.61523\" y=\"17.8457\" width=\"5.53846\" height=\"5.53846\" stroke=\"#999999\" strokeWidth=\"1.7\" strokeLinejoin=\"round\"></rect><rect x=\"17.8461\" y=\"8.61523\" width=\"5.53846\" height=\"5.53846\" stroke=\"#999999\" strokeWidth=\"1.7\" strokeLinejoin=\"round\"></rect><path d=\"M10.4615 4H4V10.4615\" stroke=\"#CCCCCC\" strokeWidth=\"1.7\" strokeLinecap=\"round\" strokeLinejoin=\"round\"></path><path d=\"M21.5385 28H28V21.5385\" stroke=\"#CCCCCC\" strokeWidth=\"1.7\" strokeLinecap=\"round\" strokeLinejoin=\"round\"></path><path d=\"M21.5385 4H28V10.4615\" stroke=\"#CCCCCC\" strokeWidth=\"1.7\" strokeLinecap=\"round\" strokeLinejoin=\"round\"></path><path d=\"M10.4615 28H4V21.5385\" stroke=\"#CCCCCC\" strokeWidth=\"1.7\" strokeLinecap=\"round\" strokeLinejoin=\"round\"></path></svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,UAAU,EAAE,SAAS,EAA0B;IACrE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BAAW,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAmD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAgD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAmD,MAAK;;;;;;0BAAiB,8OAAC;gBAAK,GAAE;gBAAU,GAAE;gBAAU,OAAM;gBAAU,QAAO;gBAAU,QAAO;gBAAU,aAAY;gBAAM,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAU,GAAE;gBAAU,OAAM;gBAAU,QAAO;gBAAU,QAAO;gBAAU,aAAY;gBAAM,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAU,GAAE;gBAAU,OAAM;gBAAU,QAAO;gBAAU,QAAO;gBAAU,aAAY;gBAAM,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAuB,QAAO;gBAAU,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAyB,QAAO;gBAAU,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAwB,QAAO;gBAAU,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;0BAAe,8OAAC;gBAAK,GAAE;gBAAwB,QAAO;gBAAU,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;;;;;;;AAE5kD", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/enum.js"], "sourcesContent": ["import AtmIcon from \"@/app/icon/atm-icon\";\r\nimport BankingIcon from \"@/app/icon/banking-icon\";\r\nimport CreditCardIcon from \"@/app/icon/creditcard-icon\";\r\nimport MomoIcon from \"@/app/icon/momo-icon\";\r\nimport VnPayIcon from \"@/app/icon/vnpay-icon\";\r\n\r\nexport const FormType = Object.freeze({\r\n  EDIT: \"Edit\",\r\n  NEW: \"New\"\r\n});\r\n\r\n// PropertyStatus Enum\r\nexport const PropertyStatus = Object.freeze({\r\n  DRAFT: \"Draft\",\r\n  PENDING_APPROVAL: \"PendingApproval\",\r\n  APPROVED: \"Approved\",\r\n  REJECTED_BY_ADMIN: \"RejectedByAdmin\",\r\n  REJECTED_DUE_TO_UNPAID: \"RejectedDueToUnpaid\",\r\n  WAITING_PAYMENT: \"WaitingPayment\",\r\n  EXPIRED: \"Expired\",\r\n  SOLD: \"Sold\"\r\n});\r\n\r\n// PropertyType Enum\r\nexport const PropertyType = Object.freeze({\r\n  SALE: \"Sale\",\r\n  RENT: \"Rent\"\r\n});\r\n\r\nexport const DEFAULT_ITEM_PER_PAGE = 10;\r\nexport const DEFAULT_PAGE = 1;\r\nexport const DEFAULT_POST_PRICE = 55000;\r\n\r\n// Enum for member ranks\r\nexport const MemberRank = Object.freeze({\r\n  DIAMOND: \"diamond\",\r\n  PLATINUM: \"platinum\",\r\n  GOLD: \"gold\",\r\n  SILVER: \"silver\",\r\n  BRONZE: \"bronze\",\r\n  DEFAULT: \"default\"\r\n});\r\n\r\n// Highlight prices based on member rank\r\nexport const highlightPrices = Object.freeze({\r\n  [MemberRank.DIAMOND]: 30000,\r\n  [MemberRank.PLATINUM]: 35000,\r\n  [MemberRank.GOLD]: 40000,\r\n  [MemberRank.SILVER]: 45000,\r\n  [MemberRank.BRONZE]: 50000,\r\n  [MemberRank.DEFAULT]: 55000,\r\n});\r\n\r\n\r\n// Default coordinates for Ho Chi Minh City\r\nexport const HCM_COORDINATES_DISTRICT_2 = Object.freeze({\r\n  latitude: 10.79, // Tọa độ ví dụ cho Quận 2 (nay thuộc TP Thủ Đức)\r\n  longitude: 106.73,\r\n  accuracy: 0 // Vị trí mặc định không có dữ liệu độ chính xác\r\n});\r\n\r\nexport const CAN_NOT_EDIT_STATUS = [\r\n    PropertyStatus.PENDING_APPROVAL,\r\n    PropertyStatus.APPROVED,\r\n    PropertyStatus.SOLD,\r\n    PropertyStatus.EXPIRED,\r\n    PropertyStatus.WAITING_PAYMENT,\r\n  ];\r\n\r\nexport const USER_TYPE = Object.freeze({\r\n  SELLER: \"Seller\",\r\n  BUYER: \"Buyer\"\r\n});\r\n\r\nexport const NOTIFICATION_TYPE = Object.freeze({\r\n  SYSTEM: \"system\",\r\n  TRANSACTION: \"transaction\",\r\n  PROMOTION: \"promotion\",\r\n  CONTACT: \"contact\",\r\n  WALLET_UPDATE: \"wallet_update\",\r\n  NEWS: \"news\",\r\n  CUSTOMER_MESSAGE: \"customer_message\",\r\n});\r\n\r\nexport const TRANSACTION_TYPE = Object.freeze({\r\n  TOP_UP: \"TOP_UP\",\r\n  PAYMENT_POST: \"PAYMENT_POST\",\r\n  PAYMENT_HIGHLIGHT: \"PAYMENT_HIGHLIGHT\",\r\n});\r\n\r\nexport const TRANSACTION_STATUS = Object.freeze({\r\n  COMPLETED: \"COMPLETED\",\r\n  PENDING: \"PENDING\",\r\n  FAILED: \"FAILED\",\r\n  CANCELLED: \"CANCELLED\",\r\n});\r\n\r\nexport const PAYMENT_METHODS = [\r\n  {\r\n    id: \"banking\",\r\n    name: \"Banking Transfer\",\r\n    icon: <BankingIcon className=\"h-6 w-6\" />,\r\n    description: \"Transfer directly from your bank account\",\r\n  },\r\n  {\r\n    id: \"momo\",\r\n    name: \"MoMo Pay\",\r\n    icon: <MomoIcon className=\"h-6 w-6\" />,\r\n    description: \"Pay with your MoMo wallet\",\r\n  },\r\n  {\r\n    id: \"credit\",\r\n    name: \"Credit Card\",\r\n    icon: <CreditCardIcon className=\"h-6 w-6\" />,\r\n    description: \"Visa, Mastercard, JCB\",\r\n  },\r\n  {\r\n    id: \"atm\",\r\n    name: \"ATM Card\",\r\n    icon: <AtmIcon className=\"h-6 w-6\" />,\r\n    description: \"Domestic ATM cards\",\r\n  },\r\n  {\r\n    id: \"vnpay\",\r\n    name: \"VNPay QR\",\r\n    icon: <VnPayIcon className=\"h-6 w-6\" />,\r\n    description: \"Scan QR code to pay\",\r\n  },\r\n]\r\n\r\nexport const PRESET_AMOUNTS = [\r\n  { value: 100000, label: \"100,000₫\" },\r\n  { value: 200000, label: \"200,000₫\" },\r\n  { value: 500000, label: \"500,000₫\" },\r\n  { value: 1000000, label: \"1,000,000₫\" },\r\n  { value: 2000000, label: \"2,000,000₫\" },\r\n  { value: 3000000, label: \"3,000,000₫\" },\r\n];"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,WAAW,OAAO,MAAM,CAAC;IACpC,MAAM;IACN,KAAK;AACP;AAGO,MAAM,iBAAiB,OAAO,MAAM,CAAC;IAC1C,OAAO;IACP,kBAAkB;IAClB,UAAU;IACV,mBAAmB;IACnB,wBAAwB;IACxB,iBAAiB;IACjB,SAAS;IACT,MAAM;AACR;AAGO,MAAM,eAAe,OAAO,MAAM,CAAC;IACxC,MAAM;IACN,MAAM;AACR;AAEO,MAAM,wBAAwB;AAC9B,MAAM,eAAe;AACrB,MAAM,qBAAqB;AAG3B,MAAM,aAAa,OAAO,MAAM,CAAC;IACtC,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAC3C,CAAC,WAAW,OAAO,CAAC,EAAE;IACtB,CAAC,WAAW,QAAQ,CAAC,EAAE;IACvB,CAAC,WAAW,IAAI,CAAC,EAAE;IACnB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,OAAO,CAAC,EAAE;AACxB;AAIO,MAAM,6BAA6B,OAAO,MAAM,CAAC;IACtD,UAAU;IACV,WAAW;IACX,UAAU,EAAE,gDAAgD;AAC9D;AAEO,MAAM,sBAAsB;IAC/B,eAAe,gBAAgB;IAC/B,eAAe,QAAQ;IACvB,eAAe,IAAI;IACnB,eAAe,OAAO;IACtB,eAAe,eAAe;CAC/B;AAEI,MAAM,YAAY,OAAO,MAAM,CAAC;IACrC,QAAQ;IACR,OAAO;AACT;AAEO,MAAM,oBAAoB,OAAO,MAAM,CAAC;IAC7C,QAAQ;IACR,aAAa;IACb,WAAW;IACX,SAAS;IACT,eAAe;IACf,MAAM;IACN,kBAAkB;AACpB;AAEO,MAAM,mBAAmB,OAAO,MAAM,CAAC;IAC5C,QAAQ;IACR,cAAc;IACd,mBAAmB;AACrB;AAEO,MAAM,qBAAqB,OAAO,MAAM,CAAC;IAC9C,WAAW;IACX,SAAS;IACT,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,kBAAkB;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,+HAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,4HAAA,CAAA,UAAQ;YAAC,WAAU;;;;;;QAC1B,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,kIAAA,CAAA,UAAc;YAAC,WAAU;;;;;;QAChC,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,2HAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,6HAAA,CAAA,UAAS;YAAC,WAAU;;;;;;QAC3B,aAAa;IACf;CACD;AAEM,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAW;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAW;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAW;IACnC;QAAE,OAAO;QAAS,OAAO;IAAa;IACtC;QAAE,OAAO;QAAS,OAAO;IAAa;IACtC;QAAE,OAAO;QAAS,OAAO;IAAa;CACvC", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/NotificationBell.jsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useState, useEffect } from \"react\";\nimport { Bell, FileText, Wallet, Gift, MessageCircle, ExternalLink } from \"lucide-react\";\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\nimport { Link } from \"@/i18n/navigation\";\nimport { useTranslations, useLocale } from \"next-intl\";\nimport { getLatestNotifications, getUnreadCount, markAsRead } from \"@/app/actions/server/notification\";\nimport { NOTIFICATION_TYPE } from \"@/lib/enum\";\n\n// Global variables to ensure only one instance runs\n// This is outside the component to ensure it's truly global\nlet globalTimeoutId = null;\nlet isInitialFetchDone = false;\n\n// NotificationBell component\nconst NotificationBell = memo(() => {\n  const t = useTranslations(\"Navbar\");\n  const locale = useLocale();\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [open, setOpen] = useState(false);\n  const [lastFetchTime, setLastFetchTime] = useState(0);\n\n  const fetchNotifications = async () => {\n    // Prevent duplicate fetches within a short time period (500ms)\n    const now = Date.now();\n    if (now - lastFetchTime < 500) {\n      return;\n    }\n\n    setLastFetchTime(now);\n    setLoading(true);\n    try {\n      const countResponse = await getUnreadCount();\n      if (countResponse.success) {\n        setUnreadCount(countResponse.data.count || 0);\n      }\n      const notifyResponse = await getLatestNotifications(10);\n      if (notifyResponse.success) {\n        setNotifications(notifyResponse.data || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching notifications:\", error);\n      // Optionally show a toast error here using a translated message\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // This effect runs once on mount and sets up the initial fetch\n  useEffect(() => {\n    // Only do the initial fetch if it hasn't been done yet\n    if (!isInitialFetchDone) {\n      fetchNotifications();\n      isInitialFetchDone = true;\n    }\n\n    // Set up a single timeout instead of an interval\n    const setupNextFetch = () => {\n      // Only set up a new timeout if there isn't one already\n      if (globalTimeoutId === null) {\n        // Set a new timeout\n        globalTimeoutId = setTimeout(() => {\n          fetchNotifications();\n\n          // Clear the global timeout ID before setting up the next one\n          globalTimeoutId = null;\n\n          // Set up the next timeout after this one completes\n          setupNextFetch();\n        }, 120000); // 2 minutes\n      }\n    };\n\n    // Only start the chain of timeouts if there isn't one already\n    if (globalTimeoutId === null) {\n      setupNextFetch();\n    }\n\n    // Clean up on unmount\n    return () => {\n      // We don't clear the global timeout here because we want it to continue\n      // even if this component instance unmounts\n    };\n  }, []);\n\n  useEffect(() => {\n    if (open) {\n      fetchNotifications();\n    }\n  }, [open]);\n\n  const handleMarkAsRead = async (id, e) => {\n    e.preventDefault(); // Prevent link navigation\n    e.stopPropagation();\n    try {\n      const response = await markAsRead({ ids: [id] });\n      if (response.success) {\n        setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)));\n        setUnreadCount((prev) => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error(\"Error marking notification as read:\", error);\n      // Optionally show a toast error here using a translated message\n    }\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case NOTIFICATION_TYPE.SYSTEM:\n        return <FileText className=\"h-4 w-4 text-blue-500\" />;\n      case NOTIFICATION_TYPE.TRANSACTION:\n        return <Wallet className=\"h-4 w-4 text-green-500\" />;\n      case NOTIFICATION_TYPE.PROMOTION:\n        return <Gift className=\"h-4 w-4 text-purple-500\" />;\n      case NOTIFICATION_TYPE.CONTACT:\n        return <MessageCircle className=\"h-4 w-4 text-orange-500\" />;\n      default:\n        return <Bell className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const formatNotificationTime = (dateString) => {\n    if (!dateString) return \"\";\n    try {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffMs = now - date;\n      const diffSeconds = Math.floor(diffMs / 1000);\n      const diffMinutes = Math.floor(diffSeconds / 60);\n      const diffHours = Math.floor(diffMinutes / 60);\n      const diffDays = Math.floor(diffHours / 24);\n\n      if (diffMinutes < 60) {\n        // Use ICU message format for plurals if available in next-intl setup\n        return t(\"notificationTimeMinutes\", { count: diffMinutes });\n      } else if (diffHours < 24) {\n        return t(\"notificationTimeHours\", { count: diffHours });\n      } else if (diffDays < 7) {\n        return t(\"notificationTimeDays\", { count: diffDays });\n      } else {\n        return date.toLocaleDateString(locale, { day: \"2-digit\", month: \"2-digit\", year: \"numeric\" });\n      }\n    } catch (error) {\n      console.error(\"Error formatting notification time:\", error);\n      return \"\";\n    }\n  };\n\n  return (\n    <Popover open={open} onOpenChange={setOpen}>\n      <PopoverTrigger asChild>\n        <div className=\"relative cursor-pointer\">\n          <Bell className=\"h-6 w-6 hover:text-coral-600\" />\n          {unreadCount > 0 && (\n            <span className=\"absolute -top-2 -right-2 bg-coral-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n              {unreadCount > 99 ? \"99+\" : unreadCount}\n            </span>\n          )}\n        </div>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-80 p-0\" align=\"end\">\n        <div className=\"p-4 border-b\">\n          <h3 className=\"font-semibold\">{t(\"notificationTitle\")}</h3>\n        </div>\n\n        <div className=\"max-h-72 overflow-auto\">\n          {loading && notifications.length === 0 ? (\n            <div className=\"flex justify-center items-center h-20\">\n              <div className=\"animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent\"></div>\n            </div>\n          ) : notifications.length > 0 ? (\n            notifications.map((notification) => (\n              <Link\n                key={notification.id}\n                href=\"/user/notifications\" // Link to the main notifications page\n                className={`block border-b last:border-0 p-3 hover:bg-gray-50 ${!notification.read ? \"bg-blue-50\" : \"\"}`}\n                onClick={() => {\n                  if (!notification.read) handleMarkAsRead(notification.id, { preventDefault: () => {}, stopPropagation: () => {} });\n                }} // Mark read on click if unread\n              >\n                <div className=\"flex gap-3\">\n                  <div className={`flex-shrink-0 mt-0.5 p-1.5 rounded-full ${notification.read ? \"bg-gray-100\" : \"bg-blue-100\"}`}>\n                    {getNotificationIcon(notification.type)}\n                  </div>\n                  <div className=\"flex-1 overflow-hidden\">\n                    <div className=\"flex justify-between items-start gap-2\">\n                      <p className={`text-sm line-clamp-2 ${notification.read ? \"font-normal\" : \"font-semibold\"}`}>\n                        {notification.title || t(\"notificationDefaultTitle\")}\n                      </p>\n                      {!notification.read && (\n                        <button\n                          onClick={(e) => handleMarkAsRead(notification.id, e)}\n                          className=\"text-xs text-blue-500 hover:underline flex-shrink-0 whitespace-nowrap\"\n                          aria-label={t(\"notificationMarkRead\")}\n                        >\n                          {t(\"notificationMarkRead\")}\n                        </button>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-gray-600 mt-1 line-clamp-2\">{notification.message}</p>\n                    <p className=\"text-xs text-gray-500 mt-1\">{formatNotificationTime(notification.createdAt)}</p>\n                  </div>\n                </div>\n              </Link>\n            ))\n          ) : (\n            <div className=\"flex flex-col items-center justify-center p-6 text-center\">\n              <Bell className=\"h-8 w-8 text-gray-300 mb-2\" />\n              <p className=\"text-sm text-gray-500\">{t(\"notificationNone\")}</p>\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-3 border-t text-center\">\n          <Link href=\"/user/notifications\" className=\"text-sm text-blue-500 hover:underline flex items-center justify-center gap-1\">\n            {t(\"notificationViewAll\")}\n            <ExternalLink className=\"h-3.5 w-3.5\" />\n          </Link>\n        </div>\n      </PopoverContent>\n    </Popover>\n  );\n});\n\nNotificationBell.displayName = \"NotificationBell\";\n\nexport default NotificationBell;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUA,oDAAoD;AACpD,4DAA4D;AAC5D,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AAEzB,6BAA6B;AAC7B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IAC5B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,qBAAqB;QACzB,+DAA+D;QAC/D,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,gBAAgB,KAAK;YAC7B;QACF;QAEA,iBAAiB;QACjB,WAAW;QACX,IAAI;YACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;YACzC,IAAI,cAAc,OAAO,EAAE;gBACzB,eAAe,cAAc,IAAI,CAAC,KAAK,IAAI;YAC7C;YACA,MAAM,iBAAiB,MAAM,CAAA,GAAA,gKAAA,CAAA,yBAAsB,AAAD,EAAE;YACpD,IAAI,eAAe,OAAO,EAAE;gBAC1B,iBAAiB,eAAe,IAAI,IAAI,EAAE;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,gEAAgE;QAClE,SAAU;YACR,WAAW;QACb;IACF;IAEA,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uDAAuD;QACvD,IAAI,CAAC,oBAAoB;YACvB;YACA,qBAAqB;QACvB;QAEA,iDAAiD;QACjD,MAAM,iBAAiB;YACrB,uDAAuD;YACvD,IAAI,oBAAoB,MAAM;gBAC5B,oBAAoB;gBACpB,kBAAkB,WAAW;oBAC3B;oBAEA,6DAA6D;oBAC7D,kBAAkB;oBAElB,mDAAmD;oBACnD;gBACF,GAAG,SAAS,YAAY;YAC1B;QACF;QAEA,8DAA8D;QAC9D,IAAI,oBAAoB,MAAM;YAC5B;QACF;QAEA,sBAAsB;QACtB,OAAO;QACL,wEAAwE;QACxE,2CAA2C;QAC7C;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,OAAO,IAAI;QAClC,EAAE,cAAc,IAAI,0BAA0B;QAC9C,EAAE,eAAe;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,EAAE;gBAAE,KAAK;oBAAC;iBAAG;YAAC;YAC9C,IAAI,SAAS,OAAO,EAAE;gBACpB,iBAAiB,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBACjF,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,GAAG,OAAO;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,gEAAgE;QAClE;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK,2GAAA,CAAA,oBAAiB,CAAC,MAAM;gBAC3B,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK,2GAAA,CAAA,oBAAiB,CAAC,WAAW;gBAChC,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,2GAAA,CAAA,oBAAiB,CAAC,SAAS;gBAC9B,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK,2GAAA,CAAA,oBAAiB,CAAC,OAAO;gBAC5B,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,CAAC,YAAY,OAAO;QACxB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,MAAM,MAAM,IAAI;YAChB,MAAM,SAAS,MAAM;YACrB,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS;YACxC,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc;YAC7C,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc;YAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;YAExC,IAAI,cAAc,IAAI;gBACpB,qEAAqE;gBACrE,OAAO,EAAE,2BAA2B;oBAAE,OAAO;gBAAY;YAC3D,OAAO,IAAI,YAAY,IAAI;gBACzB,OAAO,EAAE,yBAAyB;oBAAE,OAAO;gBAAU;YACvD,OAAO,IAAI,WAAW,GAAG;gBACvB,OAAO,EAAE,wBAAwB;oBAAE,OAAO;gBAAS;YACrD,OAAO;gBACL,OAAO,KAAK,kBAAkB,CAAC,QAAQ;oBAAE,KAAK;oBAAW,OAAO;oBAAW,MAAM;gBAAU;YAC7F;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAW,OAAM;;kCACzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAiB,EAAE;;;;;;;;;;;kCAGnC,8OAAC;wBAAI,WAAU;kCACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,GAAG,IACzB,cAAc,GAAG,CAAC,CAAC,6BACjB,8OAAC,kHAAA,CAAA,OAAI;gCAEH,MAAK,sBAAsB,sCAAsC;;gCACjE,WAAW,CAAC,kDAAkD,EAAE,CAAC,aAAa,IAAI,GAAG,eAAe,IAAI;gCACxG,SAAS;oCACP,IAAI,CAAC,aAAa,IAAI,EAAE,iBAAiB,aAAa,EAAE,EAAE;wCAAE,gBAAgB,KAAO;wCAAG,iBAAiB,KAAO;oCAAE;gCAClH;0CAEA,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,wCAAwC,EAAE,aAAa,IAAI,GAAG,gBAAgB,eAAe;sDAC3G,oBAAoB,aAAa,IAAI;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,qBAAqB,EAAE,aAAa,IAAI,GAAG,gBAAgB,iBAAiB;sEACxF,aAAa,KAAK,IAAI,EAAE;;;;;;wDAE1B,CAAC,aAAa,IAAI,kBACjB,8OAAC;4DACC,SAAS,CAAC,IAAM,iBAAiB,aAAa,EAAE,EAAE;4DAClD,WAAU;4DACV,cAAY,EAAE;sEAEb,EAAE;;;;;;;;;;;;8DAIT,8OAAC;oDAAE,WAAU;8DAA2C,aAAa,OAAO;;;;;;8DAC5E,8OAAC;oDAAE,WAAU;8DAA8B,uBAAuB,aAAa,SAAS;;;;;;;;;;;;;;;;;;+BA3BvF,aAAa,EAAE;;;;sDAiCxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAAyB,EAAE;;;;;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kHAAA,CAAA,OAAI;4BAAC,MAAK;4BAAsB,WAAU;;gCACxC,EAAE;8CACH,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;AAEA,iBAAiB,WAAW,GAAG;uCAEhB", "debugId": null}}, {"offset": {"line": 2048, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/authenticate.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse } from \"@/lib/apiUtils\";\r\nimport { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from \"@/lib/schemas/authSchema\";\r\nimport { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from \"@/lib/sessionUtils\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\nexport async function registerUser(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = registerSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(validatedFields.data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(\"Registration failed:\", response);\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, formDataObject, errorData?.message || \"Registration failed. Please try again.\");\r\n    }\r\n  } catch (error) {\r\n    return handleErrorResponse(false, formDataObject, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/dang-ki/dang-ki-thanh-cong\");\r\n}\r\n\r\nexport async function loginUser(prevState, formData) {\r\n  const validatedFields = loginSchema.safeParse({\r\n    email: formData.get(\"email\"),\r\n    password: formData.get(\"password\"),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: {\r\n        email: formData.get(\"email\"),\r\n      },\r\n    };\r\n  }\r\n\r\n  let urlCallback = \"/user/profile\";\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n        password: validatedFields.data.password,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, { email: formData.get(\"email\") }, errorData?.message || \"Thông tin đăng nhập không đúng\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const token = data.token;\r\n    const user = {\r\n      id: data.id,\r\n      fullName: data.fullName,\r\n      email: data.email,\r\n      userType: data.userType,\r\n      phone: data.phone,\r\n      lastLogin: data.lastLogin,\r\n    };\r\n\r\n    await createSession(\"Authorization\", token);\r\n    await createSession(\"UserProfile\", JSON.stringify(user));\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n  revalidatePath('/');\r\n  redirect(urlCallback);\r\n}\r\n\r\nexport async function changePassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = changePasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  const jwtData = await getJwtInfo();\r\n\r\n  let payload = {\r\n    email: jwtData.email,\r\n    oldPassword: validatedFields.data.oldPassword,\r\n    newPassword: validatedFields.data.newPassword,\r\n  };\r\n\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(payload),\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n  });\r\n}\r\n\r\nexport async function forgotPassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/da-gui-email-khoi-phuc-mat-khau\");\r\n}\r\n\r\nexport async function getUserProfile() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);\r\n}\r\n\r\nexport async function validateTokenDirectlyFromAPIServer() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);\r\n}\r\n\r\nexport async function validateTokenServer() {\r\n  const token = await getSession(\"Authorization\");\r\n  if (!token) {\r\n    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };\r\n  }\r\n\r\n  const decoded = await verifyJwtToken(token);\r\n  if (!decoded) {\r\n    deleteSession(\"Authorization\");\r\n    deleteSession(\"UserProfile\");\r\n    return { isLoggedIn: false, isExpired: true};\r\n  }\r\n\r\n  return { isLoggedIn: true, isExpired: false };\r\n}\r\n\r\nexport async function logout() {\r\n  await deleteSession(\"Authorization\");\r\n  await deleteSession(\"UserProfile\");\r\n  redirect(\"/\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4JsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AlertContext.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useState, useContext, useCallback } from 'react';\r\n\r\n// Create a context for the alert popup\r\nconst AlertContext = createContext();\r\n\r\n// AlertProvider component to wrap around your application\r\nexport const AlertProvider = ({ children }) => {\r\n  const [alert, setAlert] = useState(null);\r\n\r\n  // Function to trigger an alert\r\n  const showAlert = useCallback(({ title = \"Alert\", message, errorType, hasCancel, onCancel, onConfirm }) => {\r\n    setAlert({ title, errorType, message, hasCancel, onCancel, onConfirm });\r\n  }, []);\r\n\r\n  // Function to close the alert\r\n  const closeAlert = () => setAlert(null);\r\n\r\n  return (\r\n    <AlertContext.Provider value={{ alert, showAlert, closeAlert }}>\r\n      {children}\r\n    </AlertContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use the alert functionality\r\nexport const useAlert = () => {\r\n  return useContext(AlertContext);\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGA,uCAAuC;AACvC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAG1B,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,+BAA+B;IAC/B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,EAAE,QAAQ,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE;QACpG,SAAS;YAAE;YAAO;YAAW;YAAS;YAAW;YAAU;QAAU;IACvE,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,aAAa,IAAM,SAAS;IAElC,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAW;QAAW;kBAC1D;;;;;;AAGP;AAGO,MAAM,WAAW;IACtB,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/alert-dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref} />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-2 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title ref={ref} className={cn(\"text-lg font-semibold\", className)} {...props} />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action ref={ref} className={cn(buttonVariants(), className)} {...props} />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(buttonVariants({ variant: \"outline\" }), \"mt-2 sm:mt-0\", className)}\r\n    {...props} />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAET,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAGf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;QACjE,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC,2KAAA,CAAA,QAA0B;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAEpG,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,KAAK;QAAa,GAAG,KAAK;;;;;;AAE9F,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI,gBAAgB;QACrE,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/AlertPopup.jsx"], "sourcesContent": ["\"use client\";\r\nimport { logout } from \"@/app/actions/server/authenticate\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { useAlert } from \"@/contexts/AlertContext\";\r\n\r\nfunction AlertPopup() {\r\n  const { alert, closeAlert } = useAlert();\r\n\r\n  if (!alert) return null;\r\n\r\n  const handleCancel = () => {\r\n    alert.onCancel?.();\r\n    closeAlert();\r\n  };\r\n\r\n  const handleConfirm = async () => {\r\n    alert.onConfirm?.();\r\n    closeAlert();\r\n    if (\r\n      alert?.errorType === \"token_expired\" ||\r\n      alert?.errorType === \"unauthorized\" ||\r\n      alert?.errorType === \"no_token\"\r\n    ) {\r\n      await logout();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AlertDialog open={!!alert}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{alert.title}</AlertDialogTitle>\r\n          <AlertDialogDescription>{alert.message}</AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          {alert.hasCancel && <AlertDialogCancel onClick={handleCancel}>Cancel</AlertDialogCancel>}\r\n          <AlertDialogAction onClick={handleConfirm}>OK</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  );\r\n}\r\n\r\nexport default AlertPopup;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAUA;AAZA;;;;;AAcA,SAAS;IACP,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAErC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,eAAe;QACnB,MAAM,QAAQ;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS;QACf;QACA,IACE,OAAO,cAAc,mBACrB,OAAO,cAAc,kBACrB,OAAO,cAAc,YACrB;YACA,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD;QACb;IACF;IAEA,qBACE,8OAAC,oIAAA,CAAA,cAAW;QAAC,MAAM,CAAC,CAAC;kBACnB,cAAA,8OAAC,oIAAA,CAAA,qBAAkB;;8BACjB,8OAAC,oIAAA,CAAA,oBAAiB;;sCAChB,8OAAC,oIAAA,CAAA,mBAAgB;sCAAE,MAAM,KAAK;;;;;;sCAC9B,8OAAC,oIAAA,CAAA,yBAAsB;sCAAE,MAAM,OAAO;;;;;;;;;;;;8BAExC,8OAAC,oIAAA,CAAA,oBAAiB;;wBACf,MAAM,SAAS,kBAAI,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;sCAAc;;;;;;sCAC9D,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAKrD;uCAEe", "debugId": null}}, {"offset": {"line": 2331, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/AuthContext.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useState, useEffect, useCallback } from \"react\";\r\nimport { logout } from \"@/app/actions/server/authenticate\";\r\nimport AlertPopup from \"@/components/layout/AlertPopup\";\r\n\r\nconst AuthContext = createContext(undefined);\r\n\r\nexport function AuthProvider({ children, initialAuthState }) {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(initialAuthState.isLoggedIn);\r\n  const [isExpired, setIsExpired] = useState(initialAuthState.isExpired ?? false);\r\n\r\n  // Function to handle logout\r\n  const handleLogout = useCallback(async () => {\r\n    setIsExpired(false);\r\n    setIsLoggedIn(false);\r\n\r\n    try {\r\n      await logout();\r\n    } catch (error) {\r\n      console.error(\"Error logging out:\", error);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isExpired) {\r\n      handleLogout();\r\n    }\r\n  }, [isLoggedIn, isExpired]);\r\n\r\n  return (\r\n    <AuthContext.Provider value={{\r\n      isLoggedIn,\r\n      isExpired,\r\n      handleLogout,\r\n      setIsLoggedIn,\r\n      setIsExpired,\r\n    }}>\r\n      {children}\r\n\r\n      {/* Show alert if token is expired */}\r\n      {isExpired && (\r\n        <AlertPopup\r\n          open={isExpired}\r\n          title=\"Phiên đăng nhập hết hạn\"\r\n          message=\"Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại.\"\r\n          hasCancel={false}\r\n          onConfirm={handleLogout}\r\n        ></AlertPopup>\r\n      )}\r\n    </AuthContext.Provider>\r\n  );\r\n}\r\n\r\n// Hook to use AuthContext\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext);\r\n  if (!context) throw new Error(\"useAuth must be used within an AuthProvider\");\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAE3B,SAAS,aAAa,EAAE,QAAQ,EAAE,gBAAgB,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,UAAU;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,SAAS,IAAI;IAEzE,4BAA4B;IAC5B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,aAAa;QACb,cAAc;QAEd,IAAI;YACF,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;QACF;;YACG;YAGA,2BACC,8OAAC,mIAAA,CAAA,UAAU;gBACT,MAAM;gBACN,OAAM;gBACN,SAAQ;gBACR,WAAW;gBACX,WAAW;;;;;;;;;;;;AAKrB;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAC9B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/contexts/ProfileContext.jsx"], "sourcesContent": ["\"use client\";\r\nimport { createContext, useContext, useState, useEffect, useCallback } from \"react\";\r\nimport { getUserProfile } from \"@/app/actions/server/authenticate\";\r\nimport { useAlert } from \"./AlertContext\";\r\nimport { useAuth } from \"./AuthContext\";\r\n\r\nconst ProfileContext = createContext(null);\r\n\r\nexport function ProfileContextProvider({ children, initialUserData }) {\r\n  const [profile, setProfile] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const { showAlert } = useAlert();\r\n  const { isLoggedIn, handleLogout } = useAuth();\r\n\r\n  // Function to fetch user profile data\r\n  const fetchUserProfile = useCallback(async () => {\r\n    if (!isLoggedIn) {\r\n      setProfile(null);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await getUserProfile();\r\n      if (response && response?.success) {\r\n        // Extract wallet info from profile data\r\n        if (response?.data) {\r\n          setProfile(response?.data);\r\n        }\r\n        setError(null);\r\n      } else {\r\n        setError(response.message);\r\n        if (response.errorType === \"token_expired\" || response.errorType === \"unauthorized\" || response.errorType === \"no_token\") {\r\n          showAlert(response);\r\n          handleLogout();\r\n        }\r\n      }\r\n    } catch (err) {\r\n      setError(\"Failed to fetch user profile\");\r\n      console.error(\"Error fetching user profile:\", err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [showAlert]);\r\n\r\n  // Initial fetch when component mounts or initialUserData changes\r\n  useEffect(() => {\r\n    fetchUserProfile();\r\n  }, [initialUserData, isLoggedIn, fetchUserProfile]);\r\n\r\n  // Function to manually refresh user profile data\r\n  const refreshUserData = useCallback(async () => {\r\n    await fetchUserProfile();\r\n  }, [fetchUserProfile]);\r\n\r\n  return (\r\n    <ProfileContext.Provider\r\n      value={{\r\n        profile,\r\n        loading,\r\n        error,\r\n        refreshUserData,\r\n      }}\r\n    >\r\n      {children}\r\n    </ProfileContext.Provider>\r\n  );\r\n}\r\n\r\nexport const useProfile = () => {\r\n  const context = useContext(ProfileContext);\r\n  if (!context) {\r\n    throw new Error(\"useProfile must be used within a ProfileContextProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAE9B,SAAS,uBAAuB,EAAE,QAAQ,EAAE,eAAe,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE3C,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,CAAC,YAAY;YACf,WAAW;YACX;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;YACpC,IAAI,YAAY,UAAU,SAAS;gBACjC,wCAAwC;gBACxC,IAAI,UAAU,MAAM;oBAClB,WAAW,UAAU;gBACvB;gBACA,SAAS;YACX,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,IAAI,SAAS,SAAS,KAAK,mBAAmB,SAAS,SAAS,KAAK,kBAAkB,SAAS,SAAS,KAAK,YAAY;oBACxH,UAAU;oBACV;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAU;IAEd,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAiB;QAAY;KAAiB;IAElD,iDAAiD;IACjD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM;IACR,GAAG;QAAC;KAAiB;IAErB,qBACE,8OAAC,eAAe,QAAQ;QACtB,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2499, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/ClientNavbar.jsx"], "sourcesContent": ["\"use client\";\n\nimport {<PERSON>} from '@/i18n/navigation';\nimport {\n  Heart,\n  Menu,\n  Wallet,\n  RefreshCw,\n  ChevronDown,\n} from \"lucide-react\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from \"@/components/ui/sheet\";\nimport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuLabel,\n  DropdownMenuGroup,\n} from \"@/components/ui/dropdown-menu\";\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\";\nimport ButtonLogOut from \"../ui/ButtonLogOut\";\nimport { Button } from \"../ui/button\";\nimport { NavBarData } from \"@/common/NavBarData\";\nimport { memo, useState, useEffect } from \"react\";\nimport { getFavoritesCount } from \"@/app/actions/server/userFavorite\";\nimport { LanguageSwitcher } from \"@/components/ui/LanguageSwitcher\";\nimport { useTranslations } from 'next-intl';\nimport NotificationBell from \"./NotificationBell\";\nimport { useProfile } from \"@/contexts/ProfileContext\";\nimport { formatCurrency } from \"@/lib/utils\";\n\n// Memoized components to prevent unnecessary re-renders\nconst UserMenu = memo(() => {\n    const t = useTranslations('Navbar');\n    const { profile, loading, refreshUserData } = useProfile();\n\n    if (!profile) return null;\n\n    const walletInfo = profile?.user?.wallet;\n\n    return (\n      <DropdownMenu>\n        <DropdownMenuTrigger className=\"flex items-center space-x-2 px-2 py-1 rounded-md bg-accent hover:bg-gray-200\">\n          <Avatar className=\"h-8 w-8\">\n            <AvatarImage src={profile.user?.avatarURL} alt={profile.user?.fullName} />\n            <AvatarFallback>{profile.fullName?.substring(0, 2) || 'U'}</AvatarFallback>\n          </Avatar>\n          <span className=\"font-medium text-sm hidden sm:inline\">{profile.user?.fullName}</span>\n          <ChevronDown className=\"ml-auto h-4 w-4 text-gray-500\" />\n        </DropdownMenuTrigger>\n        <DropdownMenuContent className=\"w-56\" align=\"end\">\n          <DropdownMenuLabel>{profile.user?.fullName}</DropdownMenuLabel>\n\n          {/* Wallet Information */}\n          {walletInfo && (\n            <DropdownMenuItem className=\"flex items-center cursor-default\">\n              <Wallet className=\"mr-2 h-4 w-4 text-gray-500\" />\n              <span>{formatCurrency(walletInfo.balance)}</span>\n            </DropdownMenuItem>\n          )}\n\n          <DropdownMenuSeparator />\n\n          <DropdownMenuGroup>\n            <DropdownMenuItem asChild>\n              <Link href=\"/user/profile\">\n                {t('userMenuMyAccount')}\n              </Link>\n            </DropdownMenuItem>\n            <DropdownMenuItem asChild>\n              <Link href=\"/user/bds\">\n                {t('userMenuMyProperties')}\n              </Link>\n            </DropdownMenuItem>\n            <DropdownMenuItem asChild>\n              <Link href=\"/user/favorite\">\n                {t('userMenuMyFavorites')}\n              </Link>\n            </DropdownMenuItem>\n          </DropdownMenuGroup>\n\n          <DropdownMenuSeparator />\n\n          {/* Refresh Button */}\n          <DropdownMenuItem onClick={refreshUserData} disabled={loading}>\n            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin text-coral-500' : ''}`} />\n            <span>{t('refreshData') || 'Refresh Data'}</span>\n          </DropdownMenuItem>\n\n          <DropdownMenuSeparator />\n\n          <DropdownMenuItem>\n            <ButtonLogOut className=\"w-full text-left\">\n            </ButtonLogOut>\n          </DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n    )\n});\nUserMenu.displayName = \"UserMenu\";\n\n// This component might be removed if NavLinks in Navbar.jsx is translated\nconst MobileNavLinks = memo(() => {\n  // Placeholder - Actual translation happens in Navbar.jsx\n  return (\n    <>\n      {NavBarData.map((item) => (\n        <Link\n          key={item.id}\n          href={item.url}\n          className=\"text-lg text-navy-blue hover:text-coral-600 font-semibold\"\n        >\n          {item.name}\n        </Link>\n      ))}\n    </>\n  )\n});\nMobileNavLinks.displayName = \"MobileNavLinks\";\n\nconst FavoriteIcon = memo(({ count }) => {\n    const t = useTranslations('Navbar');\n    return (\n      <TooltipProvider>\n        <Tooltip>\n          <TooltipTrigger asChild>\n            <Link href=\"/user/favorite\" className=\"relative\">\n              <Heart className=\"h-6 w-6 cursor-pointer hover:text-coral-600\" />\n              {count > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-coral-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {count > 99 ? \"99+\" : count}\n                </span>\n              )}\n            </Link>\n          </TooltipTrigger>\n          <TooltipContent>\n            <p>{t('favoriteTooltip')}</p>\n          </TooltipContent>\n        </Tooltip>\n      </TooltipProvider>\n    )\n});\nFavoriteIcon.displayName = \"FavoriteIcon\";\n\n\nexport const ClientNavbar = memo(({ isLoggedIn }) => {\n  const t = useTranslations('Navbar');\n  const [favoriteCount, setFavoriteCount] = useState(0);\n\n  const fetchFavoriteCount = async () => {\n    if (!isLoggedIn) return;\n    try {\n      const result = await getFavoritesCount();\n      if (result.success && result.data) {\n        setFavoriteCount(result.data.count);\n      }\n    } catch (error) {\n      console.error(\"Error fetching favorite count:\", error);\n    }\n  };\n\n  useEffect(() => {\n    if (isLoggedIn) {\n      fetchFavoriteCount();\n    } else {\n      setFavoriteCount(0);\n    }\n    const handleFavoritesChanged = () => {\n      fetchFavoriteCount();\n    };\n    window.addEventListener(\"favorites-changed\", handleFavoritesChanged);\n    return () => {\n      window.removeEventListener(\"favorites-changed\", handleFavoritesChanged);\n    };\n  }, [isLoggedIn]);\n\n  return (\n    <>\n      <div className=\"hidden md:flex space-x-4 pt-2\">\n        <div className=\"flex items-center space-x-4\">\n          {isLoggedIn ? (\n            <>\n              <NotificationBell />\n              <FavoriteIcon count={favoriteCount} />\n              <UserMenu />\n              <Button asChild className=\"text-coral-500 bg-white border-coral border-2 hover:bg-coral-500 hover:text-white transition-all px-4 py-2 rounded-md\">\n                  <Link href=\"/user/bds/new\">\n                   {t('postProperty')}\n                  </Link>\n              </Button>\n            </>\n          ) : (\n            <>\n              <Link href=\"/tai-ung-dung\" className=\"font-semibold hover:text-coral-600\">\n                {t('downloadApp')}\n              </Link>\n              <div className=\"h-6 w-px bg-gray-300 mx-2\"></div>\n              <Link href=\"/dang-nhap\" className=\"font-semibold hover:text-coral-600\">\n                {t('login')}\n              </Link>\n              <div className=\"h-6 w-px bg-gray-300 mx-2\"></div>\n              <Link href=\"/dang-ki\" className=\"font-semibold hover:text-coral-600\">\n                {t('register')}\n              </Link>\n               <Button asChild className=\"bg-white text-coral-500 border border-coral-500 hover:bg-coral-500 hover:text-white transition-all px-4 py-2 rounded-md font-semibold\">\n                  <Link href=\"/user/bds/new\">\n                   {t('postProperty')}\n                  </Link>\n              </Button>\n            </>\n          )}\n          <LanguageSwitcher />\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden flex items-center gap-2\"> {/* Added gap */}\n        {/* Add Icons for mobile logged out state if needed */}\n        {!isLoggedIn && <FavoriteIcon count={favoriteCount} />}\n         {isLoggedIn && <NotificationBell />} {/* Show bell only when logged in */}\n        <Sheet>\n          <SheetTrigger asChild>\n            <button className=\"text-navy-blue p-2\">\n              <Menu className=\"h-6 w-6\" />\n            </button>\n          </SheetTrigger>\n          <SheetContent>\n            <SheetHeader>\n              <SheetTitle>{t('mobileMenuTitle')}</SheetTitle>\n            </SheetHeader>\n            <div className=\"flex flex-col space-y-4 mt-6\">\n              {/* MobileNavLinks might be replaced by translated links from Navbar.jsx later */}\n              <MobileNavLinks />\n              {isLoggedIn ? (\n                <>\n                  {/* User Info Display */}\n                  {/* User info is now displayed directly */}\n                  <div className=\"py-2 border-b border-gray-200 mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src=\"/user.jpg\" alt=\"user\" />\n                        <AvatarFallback>U</AvatarFallback>\n                      </Avatar>\n                      <div className=\"flex flex-col\">\n                        <span className=\"font-medium\">{t('userGreeting')}</span>\n                        <span className=\"text-sm text-gray-500\">User Account</span>\n                      </div>\n                    </div>\n                  </div>\n                  <Link\n                    href=\"/user/profile\"\n                    className=\"text-lg text-navy-blue hover:text-coral-500 font-semibold\"\n                  >\n                    {t('userMenuMyAccount')}\n                  </Link>\n                  <Link\n                    href=\"/user/bds\"\n                    className=\"text-lg text-navy-blue hover:text-coral-500 font-semibold\"\n                  >\n                    {t('userMenuMyProperties')}\n                  </Link>\n                  <Link\n                    href=\"/user/favorite\"\n                    className=\"text-lg text-navy-blue hover:text-coral-500 font-semibold\"\n                  >\n                    {t('userMenuMyFavorites')}\n                  </Link>\n                  <LanguageSwitcher />\n                  <Button\n                    size=\"lg\"\n                    className=\"bg-coral-500 text-white hover:bg-coral-600\"\n                    asChild\n                  >\n                    <Link href=\"/user/bds/new\">{t('postProperty')}</Link>\n                  </Button>\n                  {/* ButtonLogOut needs translation */}\n                  <ButtonLogOut></ButtonLogOut>\n                </>\n              ) : (\n                <>\n                  <LanguageSwitcher />\n                  <Link\n                    href=\"/dang-nhap\"\n                    className=\"text-lg text-coral-500 hover:text-coral-600 font-semibold\"\n                  >\n                     {t('login')}\n                  </Link>\n                   <Link\n                    href=\"/dang-ki\"\n                    className=\"text-lg text-navy-blue hover:text-coral-600 font-semibold\"\n                  >\n                     {t('register')}\n                  </Link>\n                  <Button asChild size=\"lg\" className=\"bg-coral-500 text-white hover:bg-coral-600\">\n                    <Link href=\"/user/bds/new\">{t('postProperty')}</Link>\n                  </Button>\n                </>\n              )}\n            </div>\n          </SheetContent>\n        </Sheet>\n      </div>\n    </>\n  );\n});\nClientNavbar.displayName = \"ClientNavbar\";\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;AAiCA,wDAAwD;AACxD,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IAClB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAEvD,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,aAAa,SAAS,MAAM;IAElC,qBACE,8OAAC,qIAAA,CAAA,eAAY;;0BACX,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,WAAU;;kCAC7B,8OAAC,2HAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,2HAAA,CAAA,cAAW;gCAAC,KAAK,QAAQ,IAAI,EAAE;gCAAW,KAAK,QAAQ,IAAI,EAAE;;;;;;0CAC9D,8OAAC,2HAAA,CAAA,iBAAc;0CAAE,QAAQ,QAAQ,EAAE,UAAU,GAAG,MAAM;;;;;;;;;;;;kCAExD,8OAAC;wBAAK,WAAU;kCAAwC,QAAQ,IAAI,EAAE;;;;;;kCACtE,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;0BAEzB,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;;kCAC1C,8OAAC,qIAAA,CAAA,oBAAiB;kCAAE,QAAQ,IAAI,EAAE;;;;;;oBAGjC,4BACC,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;;;;;;kCAI5C,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,qIAAA,CAAA,oBAAiB;;0CAChB,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,OAAO;0CACvB,cAAA,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;8CACR,EAAE;;;;;;;;;;;0CAGP,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,OAAO;0CACvB,cAAA,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;8CACR,EAAE;;;;;;;;;;;0CAGP,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,OAAO;0CACvB,cAAA,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;8CACR,EAAE;;;;;;;;;;;;;;;;;kCAKT,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kCAGtB,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS;wBAAiB,UAAU;;0CACpD,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,aAAa,EAAE,UAAU,gCAAgC,IAAI;;;;;;0CACpF,8OAAC;0CAAM,EAAE,kBAAkB;;;;;;;;;;;;kCAG7B,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,qIAAA,CAAA,mBAAgB;kCACf,cAAA,8OAAC,iIAAA,CAAA,UAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMpC;AACA,SAAS,WAAW,GAAG;AAEvB,0EAA0E;AAC1E,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;IAC1B,yDAAyD;IACzD,qBACE;kBACG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC,kHAAA,CAAA,OAAI;gBAEH,MAAM,KAAK,GAAG;gBACd,WAAU;0BAET,KAAK,IAAI;eAJL,KAAK,EAAE;;;;;;AAStB;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,KAAK,EAAE;IAChC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,8OAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;8BACN,8OAAC,4HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAiB,WAAU;;0CACpC,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,QAAQ,mBACP,8OAAC;gCAAK,WAAU;0CACb,QAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;8BAK9B,8OAAC,4HAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC;kCAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;AAKlB;AACA,aAAa,WAAW,GAAG;AAGpB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,UAAU,EAAE;IAC9C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY;QACjB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,iBAAiB,OAAO,IAAI,CAAC,KAAK;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd;QACF,OAAO;YACL,iBAAiB;QACnB;QACA,MAAM,yBAAyB;YAC7B;QACF;QACA,OAAO,gBAAgB,CAAC,qBAAqB;QAC7C,OAAO;YACL,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG;QAAC;KAAW;IAEf,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,2BACC;;8CACE,8OAAC,yIAAA,CAAA,UAAgB;;;;;8CACjB,8OAAC;oCAAa,OAAO;;;;;;8CACrB,8OAAC;;;;;8CACD,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACtB,cAAA,8OAAC,kHAAA,CAAA,OAAI;wCAAC,MAAK;kDACT,EAAE;;;;;;;;;;;;yDAKV;;8CACE,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAClC,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;oCAAa,WAAU;8CAC/B,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC7B,EAAE;;;;;;8CAEJ,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACvB,cAAA,8OAAC,kHAAA,CAAA,OAAI;wCAAC,MAAK;kDACT,EAAE;;;;;;;;;;;;;sCAKZ,8OAAC,qIAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;;oBAAoC;oBAEhD,CAAC,4BAAc,8OAAC;wBAAa,OAAO;;;;;;oBACnC,4BAAc,8OAAC,yIAAA,CAAA,UAAgB;;;;;oBAAI;kCACrC,8OAAC,0HAAA,CAAA,QAAK;;0CACJ,8OAAC,0HAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGpB,8OAAC,0HAAA,CAAA,eAAY;;kDACX,8OAAC,0HAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,aAAU;sDAAE,EAAE;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;;;;4CACA,2BACC;;kEAGE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2HAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,8OAAC,2HAAA,CAAA,cAAW;4EAAC,KAAI;4EAAY,KAAI;;;;;;sFACjC,8OAAC,2HAAA,CAAA,iBAAc;sFAAC;;;;;;;;;;;;8EAElB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAe,EAAE;;;;;;sFACjC,8OAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;kEAI9C,8OAAC,kHAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAU;kEAET,EAAE;;;;;;kEAEL,8OAAC,kHAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAU;kEAET,EAAE;;;;;;kEAEL,8OAAC,kHAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAU;kEAET,EAAE;;;;;;kEAEL,8OAAC,qIAAA,CAAA,mBAAgB;;;;;kEACjB,8OAAC,2HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;wDACV,OAAO;kEAEP,cAAA,8OAAC,kHAAA,CAAA,OAAI;4DAAC,MAAK;sEAAiB,EAAE;;;;;;;;;;;kEAGhC,8OAAC,iIAAA,CAAA,UAAY;;;;;;6EAGf;;kEACE,8OAAC,qIAAA,CAAA,mBAAgB;;;;;kEACjB,8OAAC,kHAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAU;kEAER,EAAE;;;;;;kEAEL,8OAAC,kHAAA,CAAA,OAAI;wDACJ,MAAK;wDACL,WAAU;kEAER,EAAE;;;;;;kEAEN,8OAAC,2HAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,MAAK;wDAAK,WAAU;kEAClC,cAAA,8OAAC,kHAAA,CAAA,OAAI;4DAAC,MAAK;sEAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD;AACA,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3242, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/hooks/use-toast.js"], "sourcesContent": ["\"use client\";\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\"\r\n}\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString();\r\n}\r\n\r\nconst toastTimeouts = new Map()\r\n\r\nconst addToRemoveQueue = (toastId) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      };\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t),\r\n      };\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t),\r\n      };\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      };\r\n  }\r\n}\r\n\r\nconst listeners = []\r\n\r\nlet memoryState = { toasts: [] }\r\n\r\nfunction dispatch(action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\nfunction toast({\r\n  ...props\r\n}) {\r\n  const id = genId()\r\n\r\n  const update = (props) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    };\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  };\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;;AACA,sCAAsC;AACtC;AAFA;;AAIA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAE3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AAEA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAO;IAC7B,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAC3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBACR;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAAY,EAAE;AAEpB,IAAI,cAAc;IAAE,QAAQ,EAAE;AAAC;AAE/B,SAAS,SAAS,MAAM;IACtB,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAEA,SAAS,MAAM,EACb,GAAG,OACJ;IACC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAY,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAClE;AACF", "debugId": null}}, {"offset": {"line": 3398, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/toast.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva } from \"class-variance-authority\";\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    (<ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props} />)\r\n  );\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}>\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold [&+div]:text-xs\", className)}\r\n    {...props} />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description ref={ref} className={cn(\"text-sm opacity-90\", className)} {...props} />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\nexport { ToastProvider, ToastViewport, Toast, ToastTitle, ToastDescription, ToastClose, ToastAction };\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AAEA;AANA;;;;;;;AAQA,MAAM,gBAAgB,iKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC,iKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAEb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IAChE,qBACG,8OAAC,iKAAA,CAAA,OAAoB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,iKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2dACA;QAED,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBACT,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC,iKAAA,CAAA,cAA2B;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QAAa,GAAG,KAAK;;;;;;AAElG,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/toaster.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    (<ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          (<Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>)\r\n        );\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>)\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAE1B,qBACG,8OAAC,0HAAA,CAAA,gBAAa;;YACZ,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACG,8OAAC,0HAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACxB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,0HAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,0HAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,0HAAA,CAAA,aAAU;;;;;;mBARA;;;;;YAWjB;0BACA,8OAAC,0HAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}]}
"use server";

import { handleErrorResponse } from "@/lib/apiUtils";
import { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from "@/lib/schemas/authSchema";
import { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from "@/lib/sessionUtils";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export async function registerUser(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = registerSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  try {
    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(validatedFields.data),
    });

    if (!response.ok) {
      console.error("Registration failed:", response);
      const errorData = await response.json().catch(() => null);
      return handleErrorResponse(false, formDataObject, errorData?.message || "Registration failed. Please try again.");
    }
  } catch (error) {
    return handleErrorResponse(false, formDataObject, "Failed to connect to the server. Please try again later.");
  }

  redirect("/dang-ki/dang-ki-thanh-cong");
}

export async function loginUser(prevState, formData) {
  const validatedFields = loginSchema.safeParse({
    email: formData.get("email"),
    password: formData.get("password"),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: {
        email: formData.get("email"),
      },
    };
  }

  let urlCallback = "/user/profile";

  try {
    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: validatedFields.data.email,
        password: validatedFields.data.password,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return handleErrorResponse(false, { email: formData.get("email") }, errorData?.message || "Thông tin đăng nhập không đúng");
    }

    const data = await response.json();
    const token = data.token;
    const user = {
      id: data.id,
      fullName: data.fullName,
      email: data.email,
      userType: data.userType,
      phone: data.phone,
      lastLogin: data.lastLogin,
    };

    await createSession("Authorization", token);
    await createSession("UserProfile", JSON.stringify(user));
  } catch (error) {
    return handleErrorResponse(false, { email: formData.get("email") }, "Failed to connect to the server. Please try again later.");
  }
  revalidatePath('/');

  await new Promise(resolve => setTimeout(resolve, 100));
  
  redirect(urlCallback);
}

export async function changePassword(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = changePasswordSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  const jwtData = await getJwtInfo();

  let payload = {
    email: jwtData.email,
    oldPassword: validatedFields.data.oldPassword,
    newPassword: validatedFields.data.newPassword,
  };

  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function forgotPassword(prevState, formData) {
  const formDataObject = Object.fromEntries(formData.entries());

  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
      fieldValues: formDataObject,
    };
  }

  try {
    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: validatedFields.data.email,
      }),
    });
  } catch (error) {
    return handleErrorResponse(false, { email: formData.get("email") }, "Failed to connect to the server. Please try again later.");
  }

  redirect("/da-gui-email-khoi-phuc-mat-khau");
}

export async function getUserProfile() {
  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);
}

export async function validateTokenDirectlyFromAPIServer() {
  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);
}

export async function validateTokenServer() {
  const token = await getSession("Authorization");
  if (!token) {
    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };
  }

  const decoded = await verifyJwtToken(token);
  if (!decoded) {
    deleteSession("Authorization");
    deleteSession("UserProfile");
    return { isLoggedIn: false, isExpired: true};
  }

  return { isLoggedIn: true, isExpired: false };
}

export async function logout() {
  await deleteSession("Authorization");
  await deleteSession("UserProfile");
  redirect("/");
}

"use client";
import { createContext, useContext, useState, useEffect, useCallback } from "react";
import { getUserProfile } from "@/app/actions/server/authenticate";
import { useAlert } from "./AlertContext";
import { useAuth } from "./AuthContext";

const ProfileContext = createContext(null);

export function ProfileContextProvider({ children, initialUserData }) {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { showAlert } = useAlert();
  const { isLoggedIn, handleLogout } = useAuth();
  console.log("ProfileContextProvider", isLoggedIn, initialUserData);

  // Function to fetch user profile data
  const fetchUserProfile = useCallback(async () => {

    console.log("fetchUserProfile isLoggedIn", isLoggedIn);
    console.log("fetchUserProfile profile", profile);

    if (!isLoggedIn) {
      setProfile(null);
      return;
    }

    setLoading(true);
    try {
      const response = await getUserProfile();
      if (response && response?.success) {
        // Extract wallet info from profile data
        if (response?.data) {
          setProfile(response?.data);
        }
        setError(null);
      } else {
        setError(response.message);
        if (response.errorType === "token_expired" || response.errorType === "unauthorized" || response.errorType === "no_token") {
          showAlert(response);
          handleLogout();
        }
      }
    } catch (err) {
      setError("Failed to fetch user profile");
      console.error("Error fetching user profile:", err);
    } finally {
      setLoading(false);
    }
  }, [showAlert, handleLogout, isLoggedIn]);

  // Initial fetch when component mounts or initialUserData changes
  useEffect(() => {
    fetchUserProfile();
  }, [initialUserData, isLoggedIn, fetchUserProfile]);

  // Function to manually refresh user profile data
  const refreshUserData = useCallback(async () => {
    await fetchUserProfile();
  }, [fetchUserProfile]);

  return (
    <ProfileContext.Provider
      value={{
        profile,
        loading,
        error,
        refreshUserData,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
}

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error("useProfile must be used within a ProfileContextProvider");
  }
  return context;
};

"use client";
import { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef } from "react";
import { getUserProfile } from "@/app/actions/server/authenticate";
import { useAlert } from "./AlertContext";
import { useAuth } from "./AuthContext";

const ProfileContext = createContext(null);

export function ProfileContextProvider({ children, initialUserData }) {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { showAlert } = useAlert();
  const { isLoggedIn, handleLogout } = useAuth();
  const isMountedRef = useRef(true);
  const lastFetchRef = useRef(0);

  console.log("ProfileContextProvider", isLoggedIn, initialUserData);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Function to fetch user profile data
  const fetchUserProfile = useCallback(async () => {
    console.log("fetchUserProfile isLoggedIn", isLoggedIn);
    console.log("fetchUserProfile profile", profile);

    if (!isLoggedIn) {
      setProfile(null);
      return;
    }

    // Prevent multiple simultaneous requests and debounce rapid calls
    const now = Date.now();
    if (loading || (now - lastFetchRef.current < 1000)) {
      console.log("Already loading profile or too soon, skipping...");
      return;
    }

    lastFetchRef.current = now;

    setLoading(true);
    try {
      const response = await getUserProfile();

      // Only update state if component is still mounted
      if (!isMountedRef.current) return;

      if (response && response?.success) {
        // Extract wallet info from profile data
        if (response?.data) {
          setProfile(response?.data);
        }
        setError(null);
      } else {
        setError(response.message);
        if (response.errorType === "token_expired" || response.errorType === "unauthorized" || response.errorType === "no_token") {
          showAlert(response);
          handleLogout();
        }
      }
    } catch (err) {
      if (!isMountedRef.current) return;
      setError("Failed to fetch user profile");
      console.error("Error fetching user profile:", err);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [isLoggedIn, loading, showAlert, handleLogout]); // Added loading to dependencies

  // Initial fetch when component mounts or when login status changes
  useEffect(() => {
    fetchUserProfile();
  }, [isLoggedIn]); // Only depend on isLoggedIn, not fetchUserProfile to avoid infinite loops

  // Function to manually refresh user profile data
  const refreshUserData = useCallback(async () => {
    if (!isLoggedIn || loading) return;

    setLoading(true);
    try {
      const response = await getUserProfile();

      if (!isMountedRef.current) return;

      if (response && response?.success) {
        if (response?.data) {
          setProfile(response?.data);
        }
        setError(null);
      } else {
        setError(response.message);
        if (response.errorType === "token_expired" || response.errorType === "unauthorized" || response.errorType === "no_token") {
          showAlert(response);
          handleLogout();
        }
      }
    } catch (err) {
      if (!isMountedRef.current) return;
      setError("Failed to fetch user profile");
      console.error("Error fetching user profile:", err);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [isLoggedIn, loading, showAlert, handleLogout]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    profile,
    loading,
    error,
    refreshUserData,
  }), [profile, loading, error, refreshUserData]);

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
}

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error("useProfile must be used within a ProfileContextProvider");
  }
  return context;
};

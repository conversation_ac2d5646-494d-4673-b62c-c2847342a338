import { Inter } from "next/font/google";
import "../globals.css";
import Footer from "@/components/layout/Footer";
import Navbar from "@/components/layout/Navbar";
import AlertPopup from "@/components/layout/AlertPopup";

import { AlertProvider } from "@/contexts/AlertContext";
import { Toaster } from "@/components/ui/toaster";
import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import { cookies } from "next/headers";
import { getMessages } from "next-intl/server";
import { AuthProvider } from "@/contexts/AuthContext";
import { validateTokenServer } from "../actions/server/authenticate";

const inter = Inter({
  variable: "--font-inter",
  weight: ["400", "700"],
  subsets: ["vietnamese"],
});

export const metadata = {
  title: "YEZHOME",
  description: "YEZHOME: <PERSON><PERSON><PERSON> n<PERSON>i n<PERSON>h chóng, giao dịch tin cậy.",
};

export default async function RootLayout({ children, params }) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const messages = await getMessages();

  // Check if user is logged in
  const initialAuthState = await validateTokenServer();
  const isLoggedIn = initialAuthState.isLoggedIn;

  let userData = null;
  if (isLoggedIn) {
    const userCookie = (await cookies()).get("UserProfile");
    if (userCookie) {
      try {
        userData = JSON.parse(userCookie.value);
      } catch (e) {
        console.error("Failed to parse user data from cookie:", e);
      }
    }
  }

  return (
    <html lang={locale}>
      <body className={`${inter.variable} antialiased`} suppressHydrationWarning>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Toaster />
          <AlertProvider>
            <AuthProvider initialAuthState={initialAuthState} initialUserData={userData}>
              <div className="min-h-screen [--header-height:calc(theme(spacing.14))]">
                <Navbar isLoggedIn={isLoggedIn}></Navbar>
                <div className="relative">{children}</div>
              </div>
              <Footer></Footer>
              <AlertPopup></AlertPopup>
            </AuthProvider>
          </AlertProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

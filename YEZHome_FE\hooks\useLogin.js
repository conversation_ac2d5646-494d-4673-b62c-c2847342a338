"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { loginSchema } from "@/lib/schemas/authSchema";

export function useLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { handleLoginSuccess } = useAuth();
  const router = useRouter();

  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate input
      const validatedFields = loginSchema.safeParse({ email, password });
      
      if (!validatedFields.success) {
        throw new Error("Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.");
      }

      // Call the API directly
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: validatedFields.data.email,
          password: validatedFields.data.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || "Thông tin đăng nhập không đúng");
      }

      const data = await response.json();
      
      // Create sessions by calling server action
      const sessionResponse = await fetch('/api/auth/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: data.token,
          user: {
            id: data.id,
            fullName: data.fullName,
            email: data.email,
            userType: data.userType,
            phone: data.phone,
            lastLogin: data.lastLogin,
          }
        }),
      });

      if (!sessionResponse.ok) {
        throw new Error("Failed to create session");
      }

      // Update client-side auth state
      handleLoginSuccess();

      // Force a page refresh to update server-side state and redirect
      window.location.href = "/user/profile";
      
      return { success: true };
      
    } catch (err) {
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    login,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}

"use client";

import { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef } from "react";
import { logout, getUserProfile } from "@/app/actions/server/authenticate";
import AlertPopup from "@/components/layout/AlertPopup";
import { useAlert } from "./AlertContext";

const AuthContext = createContext(undefined);

export function AuthProvider({ children, initialAuthState, initialUserData }) {
  // Authentication state
  const [isLoggedIn, setIsLoggedIn] = useState(initialAuthState.isLoggedIn);
  const [isExpired, setIsExpired] = useState(initialAuthState.isExpired ?? false);

  // Profile state
  const [profile, setProfile] = useState(initialUserData || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Utility refs
  const { showAlert } = useAlert();
  const isMountedRef = useRef(true);
  const lastFetchRef = useRef(0);

  console.log("AuthProvider: isLoggedIn, profile", isLoggedIn, profile);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Sync with server-side state when initialAuthState changes
  useEffect(() => {
    console.log("AuthProvider: Syncing with server state", initialAuthState);
    setIsLoggedIn(initialAuthState.isLoggedIn);
    setIsExpired(initialAuthState.isExpired ?? false);
  }, [initialAuthState.isLoggedIn, initialAuthState.isExpired]);

  // Listen for login events from server actions
  useEffect(() => {
    const handleLoginEvent = () => {
      console.log("AuthProvider: Received login event");
      setIsLoggedIn(true);
      setIsExpired(false);
    };

    window.addEventListener('user-logged-in', handleLoginEvent);
    return () => window.removeEventListener('user-logged-in', handleLoginEvent);
  }, []);

  // Function to fetch user profile data
  const fetchUserProfile = useCallback(async () => {
    console.log("fetchUserProfile isLoggedIn", isLoggedIn);
    console.log("fetchUserProfile profile", profile);

    if (!isLoggedIn) {
      setProfile(null);
      return;
    }

    // Prevent multiple simultaneous requests and debounce rapid calls
    const now = Date.now();
    if (loading || (now - lastFetchRef.current < 1000)) {
      console.log("Already loading profile or too soon, skipping...");
      return;
    }

    lastFetchRef.current = now;
    setLoading(true);

    try {
      const response = await getUserProfile();

      // Only update state if component is still mounted
      if (!isMountedRef.current) return;

      if (response && response?.success) {
        // Extract wallet info from profile data
        if (response?.data) {
          setProfile(response?.data);
        }
        setError(null);
      } else {
        setError(response.message);
        if (response.errorType === "token_expired" || response.errorType === "unauthorized" || response.errorType === "no_token") {
          showAlert(response);
          // Use logout directly to avoid circular dependency
          setIsExpired(true);
        }
      }
    } catch (err) {
      if (!isMountedRef.current) return;
      setError("Failed to fetch user profile");
      console.error("Error fetching user profile:", err);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [isLoggedIn, loading, showAlert]);

  // Function to handle login success
  const handleLoginSuccess = useCallback(() => {
    setIsLoggedIn(true);
    setIsExpired(false);
    // Automatically fetch profile after login
    setTimeout(() => fetchUserProfile(), 100);
  }, [fetchUserProfile]);

  // Function to handle logout
  const handleLogout = useCallback(async () => {
    setIsExpired(false);
    setIsLoggedIn(false);
    setProfile(null);
    setError(null);

    try {
      await logout();
    } catch (error) {
      console.error("Error logging out:", error);
    }
  }, []);

  // Function to manually refresh user profile data
  const refreshProfile = useCallback(async () => {
    if (!isLoggedIn || loading) return;

    setLoading(true);
    try {
      const response = await getUserProfile();

      if (!isMountedRef.current) return;

      if (response && response?.success) {
        if (response?.data) {
          setProfile(response?.data);
        }
        setError(null);
      } else {
        setError(response.message);
        if (response.errorType === "token_expired" || response.errorType === "unauthorized" || response.errorType === "no_token") {
          showAlert(response);
          // Use logout directly to avoid circular dependency
          setIsExpired(true);
        }
      }
    } catch (err) {
      if (!isMountedRef.current) return;
      setError("Failed to fetch user profile");
      console.error("Error fetching user profile:", err);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [isLoggedIn, loading, showAlert]);

  // Fetch profile when login status changes
  useEffect(() => {
    if (isLoggedIn) {
      fetchUserProfile();
    } else {
      setProfile(null);
      setError(null);
    }
  }, [isLoggedIn]);

  useEffect(() => {
    if (isExpired) {
      handleLogout();
    }
  }, [isLoggedIn, isExpired, handleLogout]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    // Authentication state
    isLoggedIn,
    isExpired,
    // Profile state
    profile,
    loading,
    error,
    // Functions
    handleLogout,
    handleLoginSuccess,
    refreshProfile,
    setIsLoggedIn,
    setIsExpired,
  }), [isLoggedIn, isExpired, profile, loading, error, handleLogout, handleLoginSuccess, refreshProfile]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}

      {/* Show alert if token is expired */}
      {isExpired && (
        <AlertPopup
          open={isExpired}
          title="Phiên đăng nhập hết hạn"
          message="Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
          hasCancel={false}
          onConfirm={handleLogout}
        ></AlertPopup>
      )}
    </AuthContext.Provider>
  );
}

// Hook to use AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuth must be used within an AuthProvider");
  return context;
}
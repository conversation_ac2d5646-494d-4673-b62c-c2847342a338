"use client";

import { createContext, useContext, useState, useEffect, useCallback } from "react";
import { logout } from "@/app/actions/server/authenticate";
import AlertPopup from "@/components/layout/AlertPopup";

const AuthContext = createContext(undefined);

export function AuthProvider({ children, initialAuthState }) {
  const [isLoggedIn, setIsLoggedIn] = useState(initialAuthState.isLoggedIn);
  const [isExpired, setIsExpired] = useState(initialAuthState.isExpired ?? false);

  // Listen for login events from server actions
  useEffect(() => {
    const handleLoginEvent = () => {
      setIsLoggedIn(true);
      setIsExpired(false);
    };

    window.addEventListener('user-logged-in', handleLoginEvent);
    return () => window.removeEventListener('user-logged-in', handleLoginEvent);
  }, []);

  // Function to handle login success
  const handleLoginSuccess = useCallback(() => {
    setIsLoggedIn(true);
    setIsExpired(false);
  }, []);

  // Function to handle logout
  const handleLogout = useCallback(async () => {
    setIsExpired(false);
    setIsLoggedIn(false);

    try {
      await logout();
    } catch (error) {
      console.error("Error logging out:", error);
    }
  }, []);

  useEffect(() => {
    if (isExpired) {
      handleLogout();
    }
  }, [isLoggedIn, isExpired]);

  return (
    <AuthContext.Provider value={{
      isLoggedIn,
      isExpired,
      handleLogout,
      handleLoginSuccess,
      setIsLoggedIn,
      setIsExpired,
    }}>
      {children}

      {/* Show alert if token is expired */}
      {isExpired && (
        <AlertPopup
          open={isExpired}
          title="Phiên đăng nhập hết hạn"
          message="Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
          hasCancel={false}
          onConfirm={handleLogout}
        ></AlertPopup>
      )}
    </AuthContext.Provider>
  );
}

// Hook to use AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuth must be used within an AuthProvider");
  return context;
}
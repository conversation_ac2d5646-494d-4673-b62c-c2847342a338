import { createSession } from "@/lib/sessionUtils";
import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const { token, user } = await request.json();
    
    if (!token || !user) {
      return NextResponse.json(
        { error: "Token and user data are required" },
        { status: 400 }
      );
    }

    // Create sessions
    await createSession("Authorization", token);
    await createSession("UserProfile", JSON.stringify(user));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error creating session:", error);
    return NextResponse.json(
      { error: "Failed to create session" },
      { status: 500 }
    );
  }
}

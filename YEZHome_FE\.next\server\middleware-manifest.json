{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8399fde0._.js", "server/edge/chunks/[root-of-the-server]__49146aab._.js", "server/edge/chunks/edge-wrapper_041f7dec.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=", "__NEXT_PREVIEW_MODE_ID": "4cc12a42575617155748de7538f0e39c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "11b48eb2160294f3269986e336c9101a1ea2c4a841a7464a7f3aa69c740bc5a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5e03fa75c40eacb3dd18bd4362a41918bba5b9e16698b4e2fedb6fb155fa5c1d"}}}, "sortedMiddleware": ["/"], "functions": {}}
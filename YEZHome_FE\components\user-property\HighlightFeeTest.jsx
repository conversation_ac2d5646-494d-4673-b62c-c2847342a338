"use client";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { MemberRank } from "@/lib/enum";
import { getHighlightPriceNumber } from "@/lib/memberRankUtils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import BadgeUserRank from "../layout/BadgeUserRank";

export default function HighlightFeeTest() {
  const { profile } = useAuth();
  const [highlight, setHighlight] = useState(true);
  const basePostPrice = 55000;
  
  // Get the user's rank from profile context
  const userRank = profile?.user?.memberRank || MemberRank.DEFAULT;
  
  // Calculate highlight price using the utility function
  const highlightPrice = getHighlightPriceNumber(userRank);
  const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Highlight Fee Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm text-muted-foreground">Current User Rank:</p>
          <BadgeUserRank showRefreshButton={true} />
        </div>
        
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Highlight Fee Calculation:</p>
          <div className="grid grid-cols-2 gap-2">
            <span>Base Post Price:</span>
            <span className="font-medium">{basePostPrice.toLocaleString("vi-VN")} đ</span>
            
            <span>Highlight Price:</span>
            <span className="font-medium">{highlightPrice.toLocaleString("vi-VN")} đ</span>
            
            <span>Highlight Enabled:</span>
            <span className="font-medium">{highlight ? "Yes" : "No"}</span>
            
            <span className="font-bold">Total Price:</span>
            <span className="font-bold">{totalPrice.toLocaleString("vi-VN")} đ</span>
          </div>
        </div>
        
        <Button 
          onClick={() => setHighlight(!highlight)} 
          variant="outline"
          className="w-full"
        >
          {highlight ? "Disable" : "Enable"} Highlight
        </Button>
      </CardContent>
    </Card>
  );
}

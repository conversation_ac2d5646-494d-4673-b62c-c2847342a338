"use client";
import { memo, useEffect, useState } from "react";
import { Loader2, X } from "lucide-react";
import { checkFavoriteStatus } from "@/app/actions/server/userFavorite";
import PropertyCard from "@/components/property/PropertyCard";
import Pagination from "@/components/property/Pagination";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import LoginForm from "@/components/auth/LoginForm";
import { useAuth } from "@/contexts/AuthContext";

// Main PropertyList component
function PropertyList({ 
  properties = [], 
  loading = false, 
  onPropertySelect, 
  pagination = {
    totalCount: 0,
    pageCount: 1,
    currentPage: 1,
    pageSize: 10,
    hasNextPage: false,
    hasPreviousPage: false
  },
  onPageChange,
}) {
  const [favorites, setFavorites] = useState({});
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const t = useTranslations("PropertyList");
  const { isLoggedIn } = useAuth();

  // Check favorite status when properties change and user is logged in
  useEffect(() => {
    const fetchFavoriteStatus = async () => {
      if (!isLoggedIn || properties.length === 0) return;
      
      try {
        const propertyIds = properties.map(p => p.id);
        const result = await checkFavoriteStatus(propertyIds);
        
        if (result.success) {
          const favoriteMap = {};
          result.data.forEach(item => {
            favoriteMap[item.propertyId] = true;
          });
          setFavorites(favoriteMap);
        }
      } catch (error) {
        console.error("Error fetching favorite status:", error);
      }
    };
    
    if (isLoggedIn) {
      fetchFavoriteStatus();
    }
  }, [properties, isLoggedIn]);
  
  // Handle toggling favorite status
  const handleToggleFavorite = (propertyId, isFavorite) => {

    console.log("handleToggleFavorite", propertyId, isFavorite);
    console.log("isLoggedIn", isLoggedIn);

    if (!isLoggedIn) {
      setShowLoginDialog(true);
      return;
    }
    
    setFavorites(prev => ({
      ...prev,
      [propertyId]: isFavorite
    }));
    
    // Dispatch a custom event that the navbar can listen to
    window.dispatchEvent(new CustomEvent('favorites-changed'));
  };

  if (loading) {
    return (
      <aside className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 flex items-center justify-center h-[calc(100vh-227px)]">
        <div className="flex flex-col items-center justify-center p-8">
          <Loader2 className="h-8 w-8 text-teal-500 animate-spin mb-4" />
          <p className="text-gray-500">{t("loading")}</p>
        </div>
      </aside>
    );
  }

  if (properties.length === 0) {
    return (
      <aside className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 flex items-center justify-center h-[calc(100vh-227px)]">
        <div className="text-center p-8">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">{t("noResults")}</h3>
          <p className="text-gray-500">{t("tryDifferentCriteria")}</p>
        </div>
      </aside>
    );
  }

  return (
    <>
      <aside
        className="w-full md:w-2/5 p-4 overflow-y-auto border-l border-l-stone-300 h-[calc(100vh-227px)]"
      >
        <div className="mb-4">
          <h2 className="text-lg font-semibold">
            {t("searchResults", { count: pagination.totalCount })}
          </h2>
          <p className="text-sm text-gray-500">
            {t("pageInfo", { current: pagination.currentPage, total: pagination.pageCount })}
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {properties.map((property) => (
            <PropertyCard 
              key={property.id} 
              property={property} 
              onClick={onPropertySelect}
              onToggleFavorite={handleToggleFavorite}
              isFavorite={!!favorites[property.id]}
              isLoggedIn={isLoggedIn}
            />
          ))}
        </div>
        
        {/* Pagination UI */}
        {pagination.pageCount > 1 && (
          <Pagination 
            pagination={pagination} 
            onPageChange={onPageChange} 
          />
        )}
      </aside>

      {/* Login Dialog */}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t("loginRequired")}</DialogTitle>
            <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>
          <div className="px-6 py-4">
            <LoginForm />
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                {t("dontHaveAccount")} {" "}
                <Link href="/dang-ky" className="text-coral-500 hover:text-coral-600 font-medium">
                  {t("signUpHere")}
                </Link>
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default memo(PropertyList);

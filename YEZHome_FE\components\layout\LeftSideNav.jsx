"use client";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { sidebarMenuItems } from "@/common/NavBarData";
import { usePathname } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from "@/lib/utils";
import { Copy, Wallet } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent} from "@/components/ui/card";
import BadgeUserRank from "./BadgeUserRank";

export default function LeftSideBar({
  ...props
}) {
  const pathname = usePathname();
  const t = useTranslations("UserSidebar");
  const locale = useLocale();
  const { toast } = useToast();
  const { profile } = useAuth();

  const walletInfo = profile?.user?.wallet;

  const getTranslationKey = (href) => {
    if (href.includes("/profile")) return "profile";
    if (href.includes("/payments")) return "payments";
    if (href.includes("/notifications")) return "notifications";
    if (href.includes("/bds")) return "properties";
    if (href.includes("/dashboard")) return "dashboard";
    if (href.includes("/setting")) return "setting";
    return "";
  };

  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Sao chép thành công",
      description: "Đã sao chép mã chuyển khoản vào clipboard",
    });
  };

  return (
    <Sidebar className="border-none" {...props}>
      <SidebarContent className="bg-white">
        {/* Wallet Section */}
        <Card className="m-2 bg-gray-100 rounded-sm">         
          <CardContent className="p-3">
            <div>
              <div className="text-sm font-medium flex-col items-center text-navy-blue mb-3">
                <BadgeUserRank />
              </div>
              <hr className="mb-3"></hr>
              <div className="text-sm font-medium flex items-center text-navy-blue">
                <Wallet className="mr-2 h-4 w-4" />
                {t("walletBalance")}
              </div>
              <div className="text-2xl font-bold text-navy-blue mb-3">
                {walletInfo ? formatCurrency(walletInfo?.balance) : "0₫"}
              </div>

              {profile?.user?.transferCode && (
                <div className="flex items-center justify-between mt-2 p-2 bg-white rounded border border-gray-200 mb-3">
                  <span className="text-xs text-gray-600 mr-2">
                    {t("transferCode")}{" "}
                    <span className="text-base font-semibold">{profile.user?.transferCode}</span>
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleCopyToClipboard(profile.user?.transferCode)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              )}

              <Button className="w-full" size="sm" asChild>
                <Link href="/user/wallet">{t("topUpWallet")}</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Menu */}
        <div className="p-2">
          <SidebarMenu>
            {sidebarMenuItems.map((item) => {
              const href = `/${locale}${item.href}`;
              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    isActive={pathname === item.href}
                    tooltip={t(getTranslationKey(item.href)) || item.label}
                    className={cn(
                      pathname === href ? "bg-coral-500 text-white" : "hover:bg-gray-200"
                    )}
                    asChild
                  >
                    <Link href={item.href}>
                      <item.icon size={28} />
                      <span className="font-semibold text-base">
                        {t(getTranslationKey(item.href)) || item.label}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </div>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

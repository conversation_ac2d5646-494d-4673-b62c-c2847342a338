(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/property/NearbyPropertiesCarousel.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  {
    "path": "static/chunks/node_modules_react-multi-carousel_lib_styles_b57e49f4.css",
    "included": [
      "[project]/node_modules/react-multi-carousel/lib/styles.css [app-client] (css)"
    ]
  },
  "static/chunks/node_modules_react-multi-carousel_index_bad52a21.js",
  "static/chunks/_cb2bd78d._.js",
  "static/chunks/components_property_NearbyPropertiesCarousel_jsx_57fcb12c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/NearbyPropertiesCarousel.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyImageGallery.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_1cae9a99._.js",
  "static/chunks/components_property_PropertyImageGallery_jsx_6f855c3f._.js",
  {
    "path": "static/chunks/node_modules_e87a55df._.css",
    "included": [
      "[project]/node_modules/yet-another-react-lightbox/dist/styles.css [app-client] (css)",
      "[project]/node_modules/react-photo-album/dist/styles/rows.css [app-client] (css)"
    ],
    "moduleChunks": [
      "static/chunks/node_modules_yet-another-react-lightbox_dist_styles_css_f9ee138c._.single.css",
      "static/chunks/node_modules_react-photo-album_dist_styles_rows_css_f9ee138c._.single.css"
    ]
  },
  "static/chunks/components_property_PropertyImageGallery_jsx_57fcb12c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyImageGallery.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyDescription.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/components_f7d3abb0._.js",
  "static/chunks/components_property_PropertyDescription_jsx_57fcb12c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyDescription.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyContactForm.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_6fdb9c59._.js",
  "static/chunks/components_property_PropertyContactForm_jsx_57fcb12c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyContactForm.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/DetailMap.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@goongmaps_goong-js_dist_goong-js_95be0bc1.js",
  "static/chunks/components_property_DetailMap_jsx_2fa023e2._.js",
  {
    "path": "static/chunks/node_modules_@goongmaps_goong-js_dist_goong-js_23b57252.css",
    "included": [
      "[project]/node_modules/@goongmaps/goong-js/dist/goong-js.css [app-client] (css)"
    ]
  },
  "static/chunks/components_property_DetailMap_jsx_57fcb12c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/DetailMap.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);